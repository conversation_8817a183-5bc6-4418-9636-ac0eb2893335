#!/bin/bash

# 环境检查和设置脚本
# 检�?CI/CD 环境的必需工具和配�?# 重构�?tools/ci-cd/ci/setup/environment-check.sh
# 作�? Backend API Team

# 加载通用函数�?SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"
source "$SCRIPT_DIR/../utils/common.sh"

# 检�?CI 配置文件
check_ci_config() {
    log_step "1" "验证 CI 配置文件"
    
    if ! check_file_exists ".gitlab-ci.yml" "GitLab CI 配置文件"; then
        return 1
    fi
    
    if [ ! -s ".gitlab-ci.yml" ]; then
        log_error "GitLab CI 配置文件为空"
        return 1
    fi
    
    log_success "CI 配置文件检查通过"
    return 0
}

# 检查必需的工�?check_required_tools() {
    log_step "2" "验证预装工具"
    
    local tools_status=0
    
    # 检�?Java
    log_info "检�?Java..."
    if java -version >/dev/null 2>&1; then
        local java_version=$(java -version 2>&1 | head -1)
        log_success "Java 可用: $java_version"
    else
        log_error "Java 未安装或不可�?
        tools_status=1
    fi
    
    # 检�?Maven
    log_info "检�?Maven..."
    if mvn -version >/dev/null 2>&1; then
        local maven_version=$(mvn -version | head -1)
        log_success "Maven 可用: $maven_version"
    else
        log_error "Maven 未安装或不可�?
        tools_status=1
    fi
    
    # 检�?Node.js
    log_info "检�?Node.js..."
    if node -v >/dev/null 2>&1; then
        local node_version=$(node -v)
        log_success "Node.js 可用: $node_version"
    else
        log_error "Node.js 未安装或不可�?
        tools_status=1
    fi
    
    # 检�?npm
    log_info "检�?npm..."
    if npm -v >/dev/null 2>&1; then
        local npm_version=$(npm -v)
        log_success "npm 可用: $npm_version"
    else
        log_error "npm 未安装或不可�?
        tools_status=1
    fi
    
    # 检�?Git
    log_info "检�?Git..."
    if git --version >/dev/null 2>&1; then
        local git_version=$(git --version)
        log_success "Git 可用: $git_version"
    else
        log_error "Git 未安装或不可�?
        tools_status=1
    fi
    
    # 检�?OpenAPI Generator
    log_info "检�?OpenAPI Generator..."
    local openapi_jar="/opt/openapi-generator/openapi-generator-cli-7.12.0.jar"
    if [ -f "$openapi_jar" ] && java -jar "$openapi_jar" version >/dev/null 2>&1; then
        local openapi_version=$(java -jar "$openapi_jar" version | head -1)
        log_success "OpenAPI Generator 可用: $openapi_version"
    else
        log_error "OpenAPI Generator 未安装或不可�?
        tools_status=1
    fi
    
    # 检�?ReDoc CLI
    log_info "检�?ReDoc CLI..."
    if redoc-cli --version >/dev/null 2>&1; then
        local redoc_version=$(redoc-cli --version)
        log_success "ReDoc CLI 可用: $redoc_version"
    else
        log_error "ReDoc CLI 未安装或不可�?
        tools_status=1
    fi
    
    if [ $tools_status -eq 0 ]; then
        log_success "所有工具验证通过"
        return 0
    else
        log_error "工具验证失败"
        return 1
    fi
}

# 检查项目结�?check_project_structure() {
    log_step "3" "验证项目结构"
    
    local structure_status=0
    
    # 检查关键目�?    local required_dirs=(
        "backend-apis/specifications"
        "backend-apis/specifications/services"
        "backend-apis/generators"
        "backend-apis/generators/sdks"
        "backend-apis/generators/sdks/configs"
        "backend-apis/generators/sdks/configs/java"
backend-apis/preview-docs
        "tools"
        "backend-apis/configs"
    )
    
    for dir in "${required_dirs[@]}"; do
        if check_dir_exists "$dir" "目录 $dir"; then
            log_debug "目录 $dir 存在"
        else
            log_warning "目录 $dir 不存�?
            structure_status=1
        fi
    done
    
    # 检查关键文�?    local required_files=(
        "backend-apis/configs/ci-cd/ci-settings.xml"
        "pom.xml"
    )
    
    for file in "${required_files[@]}"; do
        if check_file_exists "$file" "文件 $file"; then
            log_debug "文件 $file 存在"
        else
            log_warning "文件 $file 不存�?
            structure_status=1
        fi
    done
    
    if [ $structure_status -eq 0 ]; then
        log_success "项目结构检查通过"
        return 0
    else
        log_warning "项目结构检查发现问题，但继续执�?
        return 0  # 不阻塞执�?    fi
}

# 检查服务配�?check_services_config() {
    log_step "4" "验证服务配置"
    
    local services=$(get_services)
    local config_status=0
    
    log_info "检查服�? $services"
    
    for service in $services; do
        log_info "检查服�?$service..."
        
        if check_service_files "$service"; then
            increment_counter "services_valid"
        else
            increment_counter "services_invalid"
            config_status=1
        fi
    done
    
    local valid_count=$(get_counter "services_valid")
    local invalid_count=$(get_counter "services_invalid")
    
    log_info "服务配置检查结�? $valid_count 个有�? $invalid_count 个无�?
    
    if [ $config_status -eq 0 ]; then
        log_success "所有服务配置检查通过"
        return 0
    else
        log_warning "部分服务配置有问题，但继续执�?
        return 0  # 不阻塞执�?    fi
}

# 显示环境信息
show_environment_info() {
    log_step "5" "显示环境信息"
    
    echo ""
    log_info "🔧 环境信息汇�?"
    
    # 工具版本信息
    if java -version >/dev/null 2>&1; then
        echo "  Java: $(java -version 2>&1 | head -1)"
    fi
    
    if mvn -version >/dev/null 2>&1; then
        echo "  Maven: $(mvn -version | head -1)"
    fi
    
    if node -v >/dev/null 2>&1; then
        echo "  Node.js: $(node -v)"
    fi
    
    if npm -v >/dev/null 2>&1; then
        echo "  npm: $(npm -v)"
    fi
    
    if git --version >/dev/null 2>&1; then
        echo "  Git: $(git --version)"
    fi
    
    local openapi_jar="/opt/openapi-generator/openapi-generator-cli-7.12.0.jar"
    if [ -f "$openapi_jar" ] && java -jar "$openapi_jar" version >/dev/null 2>&1; then
        echo "  OpenAPI Generator: $(java -jar "$openapi_jar" version | head -1)"
    fi
    
    if redoc-cli --version >/dev/null 2>&1; then
        echo "  ReDoc CLI: $(redoc-cli --version)"
    fi
    
    # 环境变量信息
    echo ""
    log_info "🌍 关键环境变量:"
    echo "  CI_PROJECT_ID: ${CI_PROJECT_ID:-未设置}"
    echo "  CI_COMMIT_BRANCH: ${CI_COMMIT_BRANCH:-未设置}"
    echo "  CI_MERGE_REQUEST_IID: ${CI_MERGE_REQUEST_IID:-未设置}"
    echo "  GITLAB_TOKEN: $([ -n "$GITLAB_TOKEN" ] && echo "已设�? || echo "未设�?)"
    
    # 统计信息
    show_stats "环境检查统�?
}

# 主函�?main() {
    init_script "环境检�? "检�?CI/CD 环境的必需工具和配�?
    
    local overall_status=0
    
    # 执行各项检�?    if ! check_ci_config; then
        overall_status=1
    fi
    
    if ! check_required_tools; then
        overall_status=1
    fi
    
    check_project_structure  # 不影响整体状�?    check_services_config    # 不影响整体状�?    
    show_environment_info
    
    if [ $overall_status -eq 0 ]; then
        log_success "🎉 环境检查完成，所有必需组件都可用！"
        finish_script "环境检�? "true"
        return 0
    else
        log_error "�?环境检查失败，请检查缺失的组件"
        finish_script "环境检�? "false"
        return 1
    fi
}

# 如果直接执行此脚�?if [[ "${BASH_SOURCE[0]}" == "${0}" ]]; then
    main "$@"
fi

log_info "环境检查模块已加载 �?



