'!include': '../../shared/shared-data-mappings.yaml'
generatorName: "java"
inputSpec: "specifications/services/camera/openapi.yaml"
outputDir: "build/sdks/camera/java"
templateDir: "generators/sdks/templates/java"
additionalProperties:
  groupId: "com.manycore"
  artifactId: "camera-rest-client"
  artifactVersion: "0.0.1-SNAPSHOT"
  modelPackage: "com.manycore.camera.client.model"
  apiPackage: "com.qunhe.diy.camera.client.api"
  invokerPackage: "com.qunhe.diy.camera.client"
  java8: true
  dateLibrary: "java8"
  library: "resttemplate"
  sourceFolder: "src/main/java"
  hideGenerationTimestamp: true
  serializationLibrary: "jackson"
  useRuntimeException: false

    

