#!/bin/bash

# Docker 镜像拉取重试脚本
# 用于处理镜像拉取超时和网络问题

set -e

# 默认配置
DEFAULT_RETRIES=3
DEFAULT_DELAY=30
DEFAULT_TIMEOUT=600

# 从环境变量获取配置
RETRIES=${IMAGE_PULL_RETRIES:-$DEFAULT_RETRIES}
DELAY=${IMAGE_PULL_DELAY:-$DEFAULT_DELAY}
TIMEOUT=${DOCKER_PULL_TIMEOUT:-$DEFAULT_TIMEOUT}

# 颜色输出
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

log_info() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

log_warn() {
    echo -e "${YELLOW}[WARN]${NC} $1"
}

log_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

log_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

# 检查镜像是否存在于本地
check_local_image() {
    local image=$1
    if docker images --format "table {{.Repository}}:{{.Tag}}" | grep -q "^${image}$"; then
        return 0
    else
        return 1
    fi
}

# 拉取镜像的核心函数
pull_image_with_retry() {
    local image=$1
    local attempt=1
    
    log_info "开始拉取镜像: $image"
    log_info "配置: 重试次数=$RETRIES, 延迟=${DELAY}s, 超时=${TIMEOUT}s"
    
    while [ $attempt -le $RETRIES ]; do
        log_info "第 $attempt/$RETRIES 次尝试拉取镜像..."
        
        # 使用 timeout 命令限制拉取时间
        if timeout $TIMEOUT docker pull "$image"; then
            log_success "镜像拉取成功: $image"
            return 0
        else
            local exit_code=$?
            if [ $exit_code -eq 124 ]; then
                log_error "镜像拉取超时 (${TIMEOUT}s)"
            else
                log_error "镜像拉取失败，退出码: $exit_code"
            fi
            
            if [ $attempt -lt $RETRIES ]; then
                log_warn "等待 ${DELAY} 秒后重试..."
                sleep $DELAY
            fi
        fi
        
        attempt=$((attempt + 1))
    done
    
    log_error "所有重试都失败了，无法拉取镜像: $image"
    return 1
}

# 主函数
main() {
    local primary_image="${DOCKER_IMAGE:-registry-qunhe.qunhequnhe.com/display/openapi-generator:latest}"
    local fallback_image="${FALLBACK_IMAGE:-}"
    
    log_info "=== Docker 镜像拉取脚本 ==="
    log_info "主镜像: $primary_image"
    log_info "备用镜像: ${fallback_image:-无}"
    
    # 检查本地是否已有镜像
    if check_local_image "$primary_image"; then
        log_success "本地已存在主镜像，跳过拉取: $primary_image"
        export DOCKER_IMAGE="$primary_image"
        return 0
    fi
    
    # 尝试拉取主镜像
    if pull_image_with_retry "$primary_image"; then
        export DOCKER_IMAGE="$primary_image"
        return 0
    fi
    
    # 如果主镜像失败，尝试备用镜像
    if [ -n "$fallback_image" ]; then
        log_warn "主镜像拉取失败，尝试备用镜像..."
        
        # 检查本地是否已有备用镜像
        if check_local_image "$fallback_image"; then
            log_success "本地已存在备用镜像: $fallback_image"
            export DOCKER_IMAGE="$fallback_image"
            return 0
        fi
        
        # 尝试拉取备用镜像
        if pull_image_with_retry "$fallback_image"; then
            export DOCKER_IMAGE="$fallback_image"
            log_success "成功使用备用镜像: $fallback_image"
            return 0
        fi
    fi
    
    log_error "所有镜像拉取都失败了！"
    log_error "请检查："
    log_error "1. 网络连接是否正常"
    log_error "2. 镜像仓库是否可访问"
    log_error "3. 认证信息是否正确"
    log_error "4. 镜像名称和标签是否正确"
    
    return 1
}

# 如果脚本被直接执行
if [ "${BASH_SOURCE[0]}" == "${0}" ]; then
    main "$@"
fi
