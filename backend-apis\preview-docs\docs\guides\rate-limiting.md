---
id: rate-limiting
title: 速率限制
sidebar_label: 速率限制
---

# API 速率限制

为了确保服务的稳定性和公平使用，群核科技 API 实施了速率限制机制。了解这些限制并在您的应用中正确处理，是构建稳定应用的关键。

## 📊 限制概览

### 全局限制

| 计划类型 | 每分钟请求数 | 每小时请求数 | 每日请求数 |
|----------|-------------|-------------|-----------|
| 免费版 | 100 | 1,000 | 10,000 |
| 基础版 | 500 | 10,000 | 100,000 |
| 专业版 | 2,000 | 50,000 | 500,000 |
| 企业版 | 5,000 | 200,000 | 2,000,000 |

### 端点特定限制

某些资源密集型端点有额外限制：

| 端点类型 | 限制 | 说明 |
|----------|------|------|
| 批量操作 | 每分钟 10 次 | `*/batch-*` 端点 |
| 文件上传 | 每分钟 5 次 | 模型和图片上传 |
| 渲染请求 | 每分钟 20 次 | 3D 渲染服务 |
| 搜索 API | 每分钟 200 次 | 模型搜索和筛选 |

## 🔍 速率限制检测

### 响应头信息

每个 API 响应都包含速率限制相关的头信息：

```http
HTTP/1.1 200 OK
X-RateLimit-Limit: 1000
X-RateLimit-Remaining: 999
X-RateLimit-Reset: 1640995200
X-RateLimit-Window: 3600
Retry-After: 42
```

| 头信息 | 说明 |
|--------|------|
| `X-RateLimit-Limit` | 当前时间窗口的请求限制 |
| `X-RateLimit-Remaining` | 当前时间窗口剩余请求数 |
| `X-RateLimit-Reset` | 限制重置的时间戳 |
| `X-RateLimit-Window` | 时间窗口长度（秒） |
| `Retry-After` | 建议重试间隔（秒） |

### 429 状态码

当达到速率限制时，API 返回 `429 Too Many Requests`：

```json
{
  "error": {
    "code": "RATE_LIMITED",
    "message": "请求频率过高，请稍后重试",
    "details": {
      "limit": 1000,
      "window": 3600,
      "retry_after": 42
    }
  }
}
```

## 🛠️ 处理速率限制

### 1. 基础处理

```javascript
async function makeApiRequest(url, options) {
  try {
    const response = await fetch(url, options);
    
    if (response.status === 429) {
      const retryAfter = response.headers.get('Retry-After');
      throw new RateLimitError(`请求被限制，请在 ${retryAfter} 秒后重试`);
    }
    
    return response;
  } catch (error) {
    if (error instanceof RateLimitError) {
      console.warn('遇到速率限制:', error.message);
      throw error;
    }
    throw error;
  }
}

class RateLimitError extends Error {
  constructor(message) {
    super(message);
    this.name = 'RateLimitError';
  }
}
```

### 2. 自动重试机制

```javascript
class ApiClient {
  async requestWithRetry(url, options, maxRetries = 3) {
    for (let attempt = 1; attempt <= maxRetries; attempt++) {
      try {
        const response = await fetch(url, options);
        
        if (response.status === 429) {
          if (attempt === maxRetries) {
            throw new Error('重试次数已达上限');
          }
          
          const retryAfter = parseInt(response.headers.get('Retry-After') || '60');
          console.log(`速率限制触发，${retryAfter}秒后重试 (尝试 ${attempt}/${maxRetries})`);
          
          await this.sleep(retryAfter * 1000);
          continue;
        }
        
        return response;
      } catch (error) {
        if (attempt === maxRetries) throw error;
        
        // 指数退避
        const delay = Math.pow(2, attempt) * 1000;
        await this.sleep(delay);
      }
    }
  }
  
  sleep(ms) {
    return new Promise(resolve => setTimeout(resolve, ms));
  }
}
```

### 3. 智能限流器

```javascript
class RateLimiter {
  constructor(maxRequests = 100, windowMs = 60000) {
    this.maxRequests = maxRequests;
    this.windowMs = windowMs;
    this.requests = [];
    this.queue = [];
  }
  
  async throttle(fn) {
    return new Promise((resolve, reject) => {
      this.queue.push({ fn, resolve, reject });
      this.processQueue();
    });
  }
  
  async processQueue() {
    if (this.queue.length === 0) return;
    
    // 清理过期请求
    const now = Date.now();
    this.requests = this.requests.filter(time => now - time < this.windowMs);
    
    if (this.requests.length < this.maxRequests) {
      const { fn, resolve, reject } = this.queue.shift();
      this.requests.push(now);
      
      try {
        const result = await fn();
        resolve(result);
      } catch (error) {
        reject(error);
      }
      
      // 处理下一个请求
      setTimeout(() => this.processQueue(), 10);
    } else {
      // 等待到下一个时间窗口
      const oldestRequest = Math.min(...this.requests);
      const waitTime = this.windowMs - (now - oldestRequest);
      setTimeout(() => this.processQueue(), waitTime);
    }
  }
}

// 使用示例
const limiter = new RateLimiter(100, 60000); // 每分钟100请求

async function getResource(id) {
  return limiter.throttle(async () => {
    return await api.get(`/resource/${id}`);
  });
}
```

## 📈 监控和优化

### 1. 速率限制监控

```javascript
class RateLimitMonitor {
  constructor() {
    this.metrics = {
      requests: 0,
      rateLimited: 0,
      remaining: null,
      resetTime: null
    };
  }
  
  updateFromResponse(response) {
    this.metrics.requests++;
    this.metrics.remaining = parseInt(response.headers.get('X-RateLimit-Remaining'));
    this.metrics.resetTime = parseInt(response.headers.get('X-RateLimit-Reset'));
    
    if (response.status === 429) {
      this.metrics.rateLimited++;
    }
  }
  
  getStatus() {
    const rateLimitRate = this.metrics.rateLimited / this.metrics.requests;
    return {
      ...this.metrics,
      rateLimitRate,
      status: rateLimitRate > 0.05 ? 'warning' : 'healthy'
    };
  }
  
  shouldWarn() {
    return this.metrics.remaining !== null && this.metrics.remaining < 50;
  }
}
```

### 2. 请求分布优化

```javascript
class RequestDistributor {
  constructor(maxConcurrent = 10) {
    this.maxConcurrent = maxConcurrent;
    this.running = 0;
    this.queue = [];
  }
  
  async execute(requests) {
    return Promise.all(requests.map(request => this.enqueue(request)));
  }
  
  async enqueue(request) {
    return new Promise((resolve, reject) => {
      this.queue.push({ request, resolve, reject });
      this.process();
    });
  }
  
  async process() {
    if (this.running >= this.maxConcurrent || this.queue.length === 0) {
      return;
    }
    
    this.running++;
    const { request, resolve, reject } = this.queue.shift();
    
    try {
      const result = await request();
      resolve(result);
    } catch (error) {
      reject(error);
    } finally {
      this.running--;
      this.process(); // 处理下一个请求
    }
  }
}
```

## 💡 优化策略

### 1. 批量操作

优先使用批量 API 减少请求数量：

```javascript
// ✅ 推荐：批量获取
const furnitureList = await api.furniture.batchGet({
  ids: ['id1', 'id2', 'id3', 'id4', 'id5']
});

// ❌ 避免：多次单独请求
const results = await Promise.all([
  api.furniture.get('id1'),
  api.furniture.get('id2'),
  api.furniture.get('id3'),
  api.furniture.get('id4'),
  api.furniture.get('id5')
]);
```

### 2. 智能缓存

实现多层缓存策略：

```javascript
class SmartCache {
  constructor() {
    this.memoryCache = new Map();
    this.diskCache = new LocalStorage(); // 或其他持久化存储
  }
  
  async get(key) {
    // 优先从内存缓存获取
    if (this.memoryCache.has(key)) {
      return this.memoryCache.get(key);
    }
    
    // 其次从磁盘缓存获取
    const cached = await this.diskCache.get(key);
    if (cached && !this.isExpired(cached)) {
      this.memoryCache.set(key, cached.data);
      return cached.data;
    }
    
    return null;
  }
  
  async set(key, data, ttl = 300000) { // 默认5分钟
    const item = {
      data,
      expires: Date.now() + ttl
    };
    
    this.memoryCache.set(key, data);
    await this.diskCache.set(key, item);
  }
  
  isExpired(item) {
    return Date.now() > item.expires;
  }
}
```

### 3. 请求合并

合并相似的请求：

```javascript
class RequestMerger {
  constructor(mergeWindow = 100) {
    this.mergeWindow = mergeWindow;
    this.pendingRequests = new Map();
  }
  
  async get(endpoint, params) {
    const key = `${endpoint}:${JSON.stringify(params)}`;
    
    if (this.pendingRequests.has(key)) {
      return this.pendingRequests.get(key);
    }
    
    const promise = this.executeRequest(endpoint, params);
    this.pendingRequests.set(key, promise);
    
    setTimeout(() => {
      this.pendingRequests.delete(key);
    }, this.mergeWindow);
    
    return promise;
  }
  
  async executeRequest(endpoint, params) {
    // 实际的 API 请求
    return await api.get(endpoint, params);
  }
}
```

## ⚠️ 常见错误

### 1. 忽略响应头

```javascript
// ❌ 错误：不检查剩余配额
await api.get('/resource');

// ✅ 正确：检查配额状态
const response = await api.get('/resource');
const remaining = response.headers.get('X-RateLimit-Remaining');
if (remaining < 10) {
  console.warn('API 配额即将用完');
}
```

### 2. 过度重试

```javascript
// ❌ 错误：无限重试
async function badRetry() {
  while (true) {
    try {
      return await api.get('/resource');
    } catch (error) {
      if (error.status === 429) {
        await sleep(1000); // 固定延迟
        continue;
      }
      throw error;
    }
  }
}

// ✅ 正确：有限重试 + 指数退避
async function goodRetry(maxRetries = 3) {
  for (let i = 0; i < maxRetries; i++) {
    try {
      return await api.get('/resource');
    } catch (error) {
      if (error.status === 429 && i < maxRetries - 1) {
        const delay = Math.pow(2, i) * 1000;
        await sleep(delay);
        continue;
      }
      throw error;
    }
  }
}
```

## 📞 获取帮助

如果您的应用经常遇到速率限制：

1. **升级计划** - 考虑升级到更高级别的 API 计划
2. **优化请求** - 审查并优化您的 API 使用模式
3. **联系支持** - 如需定制限制，请联系 <EMAIL>

更多信息请参考：
- [最佳实践指南](/docs/guides/best-practices)
- [错误处理](/docs/getting-started/error-handling)
- [开发者控制台](https://developers.qunheco.com)

## 📚 更多资源

- [开发者控制台](https://developers.qunheco.com) 