import React from 'react';
import ComponentCreator from '@docusaurus/ComponentCreator';

export default [
  {
    path: '/manycoreapi-demo/0.0.4/search',
    component: ComponentCreator('/manycoreapi-demo/0.0.4/search', 'a1e'),
    exact: true
  },
  {
    path: '/manycoreapi-demo/0.0.4/docs',
    component: ComponentCreator('/manycoreapi-demo/0.0.4/docs', 'f28'),
    routes: [
      {
        path: '/manycoreapi-demo/0.0.4/docs',
        component: ComponentCreator('/manycoreapi-demo/0.0.4/docs', '41a'),
        routes: [
          {
            path: '/manycoreapi-demo/0.0.4/docs',
            component: ComponentCreator('/manycoreapi-demo/0.0.4/docs', '0b4'),
            routes: [
              {
                path: '/manycoreapi-demo/0.0.4/docs/',
                component: ComponentCreator('/manycoreapi-demo/0.0.4/docs/', 'cc3'),
                exact: true,
                sidebar: "tutorialSidebar"
              },
              {
                path: '/manycoreapi-demo/0.0.4/docs/api/camera/camera-infrastructure',
                component: ComponentCreator('/manycoreapi-demo/0.0.4/docs/api/camera/camera-infrastructure', '692'),
                exact: true,
                sidebar: "cameraSidebar"
              },
              {
                path: '/manycoreapi-demo/0.0.4/docs/api/camera/camera-infrastructure-api',
                component: ComponentCreator('/manycoreapi-demo/0.0.4/docs/api/camera/camera-infrastructure-api', '957'),
                exact: true,
                sidebar: "cameraSidebar"
              },
              {
                path: '/manycoreapi-demo/0.0.4/docs/api/camera/get-cameras',
                component: ComponentCreator('/manycoreapi-demo/0.0.4/docs/api/camera/get-cameras', 'ba4'),
                exact: true,
                sidebar: "cameraSidebar"
              },
              {
                path: '/manycoreapi-demo/0.0.4/docs/api/doorwindow/batch-update',
                component: ComponentCreator('/manycoreapi-demo/0.0.4/docs/api/doorwindow/batch-update', 'fba'),
                exact: true,
                sidebar: "doorwindowSidebar"
              },
              {
                path: '/manycoreapi-demo/0.0.4/docs/api/doorwindow/get-doc',
                component: ComponentCreator('/manycoreapi-demo/0.0.4/docs/api/doorwindow/get-doc', '6af'),
                exact: true,
                sidebar: "doorwindowSidebar"
              },
              {
                path: '/manycoreapi-demo/0.0.4/docs/api/doorwindow/门窗管理-api',
                component: ComponentCreator('/manycoreapi-demo/0.0.4/docs/api/doorwindow/门窗管理-api', '42a'),
                exact: true,
                sidebar: "doorwindowSidebar"
              },
              {
                path: '/manycoreapi-demo/0.0.4/docs/api/doorwindow/门窗设计管理api',
                component: ComponentCreator('/manycoreapi-demo/0.0.4/docs/api/doorwindow/门窗设计管理api', '646'),
                exact: true,
                sidebar: "doorwindowSidebar"
              },
              {
                path: '/manycoreapi-demo/0.0.4/docs/api/furniture/batch-create-furniture',
                component: ComponentCreator('/manycoreapi-demo/0.0.4/docs/api/furniture/batch-create-furniture', '494'),
                exact: true,
                sidebar: "furnitureSidebar"
              },
              {
                path: '/manycoreapi-demo/0.0.4/docs/api/furniture/batch-create-furniture-by-group-product',
                component: ComponentCreator('/manycoreapi-demo/0.0.4/docs/api/furniture/batch-create-furniture-by-group-product', '5fb'),
                exact: true,
                sidebar: "furnitureSidebar"
              },
              {
                path: '/manycoreapi-demo/0.0.4/docs/api/furniture/batch-delete-furniture',
                component: ComponentCreator('/manycoreapi-demo/0.0.4/docs/api/furniture/batch-delete-furniture', '6dd'),
                exact: true,
                sidebar: "furnitureSidebar"
              },
              {
                path: '/manycoreapi-demo/0.0.4/docs/api/furniture/batch-get-furniture',
                component: ComponentCreator('/manycoreapi-demo/0.0.4/docs/api/furniture/batch-get-furniture', 'b82'),
                exact: true,
                sidebar: "furnitureSidebar"
              },
              {
                path: '/manycoreapi-demo/0.0.4/docs/api/furniture/batch-update-furniture',
                component: ComponentCreator('/manycoreapi-demo/0.0.4/docs/api/furniture/batch-update-furniture', 'abc'),
                exact: true,
                sidebar: "furnitureSidebar"
              },
              {
                path: '/manycoreapi-demo/0.0.4/docs/api/furniture/create-furniture',
                component: ComponentCreator('/manycoreapi-demo/0.0.4/docs/api/furniture/create-furniture', 'eb3'),
                exact: true,
                sidebar: "furnitureSidebar"
              },
              {
                path: '/manycoreapi-demo/0.0.4/docs/api/furniture/delete-furniture',
                component: ComponentCreator('/manycoreapi-demo/0.0.4/docs/api/furniture/delete-furniture', '104'),
                exact: true,
                sidebar: "furnitureSidebar"
              },
              {
                path: '/manycoreapi-demo/0.0.4/docs/api/furniture/get-furniture-list',
                component: ComponentCreator('/manycoreapi-demo/0.0.4/docs/api/furniture/get-furniture-list', '267'),
                exact: true,
                sidebar: "furnitureSidebar"
              },
              {
                path: '/manycoreapi-demo/0.0.4/docs/api/furniture/get-single-furniture',
                component: ComponentCreator('/manycoreapi-demo/0.0.4/docs/api/furniture/get-single-furniture', 'a93'),
                exact: true,
                sidebar: "furnitureSidebar"
              },
              {
                path: '/manycoreapi-demo/0.0.4/docs/api/furniture/update-furniture',
                component: ComponentCreator('/manycoreapi-demo/0.0.4/docs/api/furniture/update-furniture', '884'),
                exact: true,
                sidebar: "furnitureSidebar"
              },
              {
                path: '/manycoreapi-demo/0.0.4/docs/api/furniture/家具管理接口',
                component: ComponentCreator('/manycoreapi-demo/0.0.4/docs/api/furniture/家具管理接口', '42e'),
                exact: true,
                sidebar: "furnitureSidebar"
              },
              {
                path: '/manycoreapi-demo/0.0.4/docs/api/furniture/家具设计管理api',
                component: ComponentCreator('/manycoreapi-demo/0.0.4/docs/api/furniture/家具设计管理api', 'db0'),
                exact: true,
                sidebar: "furnitureSidebar"
              },
              {
                path: '/manycoreapi-demo/0.0.4/docs/api/koolux/batch-update',
                component: ComponentCreator('/manycoreapi-demo/0.0.4/docs/api/koolux/batch-update', '99d'),
                exact: true,
                sidebar: "kooluxSidebar"
              },
              {
                path: '/manycoreapi-demo/0.0.4/docs/api/koolux/get-scene-document',
                component: ComponentCreator('/manycoreapi-demo/0.0.4/docs/api/koolux/get-scene-document', '83d'),
                exact: true,
                sidebar: "kooluxSidebar"
              },
              {
                path: '/manycoreapi-demo/0.0.4/docs/api/koolux/import-data-source',
                component: ComponentCreator('/manycoreapi-demo/0.0.4/docs/api/koolux/import-data-source', 'f03'),
                exact: true,
                sidebar: "kooluxSidebar"
              },
              {
                path: '/manycoreapi-demo/0.0.4/docs/api/koolux/koo-lux-light',
                component: ComponentCreator('/manycoreapi-demo/0.0.4/docs/api/koolux/koo-lux-light', '367'),
                exact: true,
                sidebar: "kooluxSidebar"
              },
              {
                path: '/manycoreapi-demo/0.0.4/docs/api/koolux/koolux-rest-api',
                component: ComponentCreator('/manycoreapi-demo/0.0.4/docs/api/koolux/koolux-rest-api', 'f40'),
                exact: true,
                sidebar: "kooluxSidebar"
              },
              {
                path: '/manycoreapi-demo/0.0.4/docs/api/layout/batch-create-legend',
                component: ComponentCreator('/manycoreapi-demo/0.0.4/docs/api/layout/batch-create-legend', '469'),
                exact: true,
                sidebar: "layoutSidebar"
              },
              {
                path: '/manycoreapi-demo/0.0.4/docs/api/layout/batch-create-legend-group',
                component: ComponentCreator('/manycoreapi-demo/0.0.4/docs/api/layout/batch-create-legend-group', '231'),
                exact: true,
                sidebar: "layoutSidebar"
              },
              {
                path: '/manycoreapi-demo/0.0.4/docs/api/layout/batch-delete-legend',
                component: ComponentCreator('/manycoreapi-demo/0.0.4/docs/api/layout/batch-delete-legend', '997'),
                exact: true,
                sidebar: "layoutSidebar"
              },
              {
                path: '/manycoreapi-demo/0.0.4/docs/api/layout/batch-get-legend',
                component: ComponentCreator('/manycoreapi-demo/0.0.4/docs/api/layout/batch-get-legend', '7f3'),
                exact: true,
                sidebar: "layoutSidebar"
              },
              {
                path: '/manycoreapi-demo/0.0.4/docs/api/layout/batch-update-legend',
                component: ComponentCreator('/manycoreapi-demo/0.0.4/docs/api/layout/batch-update-legend', '15f'),
                exact: true,
                sidebar: "layoutSidebar"
              },
              {
                path: '/manycoreapi-demo/0.0.4/docs/api/layout/create-legend',
                component: ComponentCreator('/manycoreapi-demo/0.0.4/docs/api/layout/create-legend', 'f86'),
                exact: true,
                sidebar: "layoutSidebar"
              },
              {
                path: '/manycoreapi-demo/0.0.4/docs/api/layout/delete-legend',
                component: ComponentCreator('/manycoreapi-demo/0.0.4/docs/api/layout/delete-legend', '7b7'),
                exact: true,
                sidebar: "layoutSidebar"
              },
              {
                path: '/manycoreapi-demo/0.0.4/docs/api/layout/get-colored-floor-plan',
                component: ComponentCreator('/manycoreapi-demo/0.0.4/docs/api/layout/get-colored-floor-plan', '101'),
                exact: true,
                sidebar: "layoutSidebar"
              },
              {
                path: '/manycoreapi-demo/0.0.4/docs/api/layout/get-single-legend',
                component: ComponentCreator('/manycoreapi-demo/0.0.4/docs/api/layout/get-single-legend', '3fe'),
                exact: true,
                sidebar: "layoutSidebar"
              },
              {
                path: '/manycoreapi-demo/0.0.4/docs/api/layout/list-legend',
                component: ComponentCreator('/manycoreapi-demo/0.0.4/docs/api/layout/list-legend', '60b'),
                exact: true,
                sidebar: "layoutSidebar"
              },
              {
                path: '/manycoreapi-demo/0.0.4/docs/api/layout/update-legend',
                component: ComponentCreator('/manycoreapi-demo/0.0.4/docs/api/layout/update-legend', 'a1f'),
                exact: true,
                sidebar: "layoutSidebar"
              },
              {
                path: '/manycoreapi-demo/0.0.4/docs/api/layout/图例管理接口',
                component: ComponentCreator('/manycoreapi-demo/0.0.4/docs/api/layout/图例管理接口', 'f7f'),
                exact: true,
                sidebar: "layoutSidebar"
              },
              {
                path: '/manycoreapi-demo/0.0.4/docs/api/layout/户型图例管理api',
                component: ComponentCreator('/manycoreapi-demo/0.0.4/docs/api/layout/户型图例管理api', 'dc2'),
                exact: true,
                sidebar: "layoutSidebar"
              },
              {
                path: '/manycoreapi-demo/0.0.4/docs/api/pdm/batch-delete-planar-model',
                component: ComponentCreator('/manycoreapi-demo/0.0.4/docs/api/pdm/batch-delete-planar-model', 'cdf'),
                exact: true,
                sidebar: "pdmSidebar"
              },
              {
                path: '/manycoreapi-demo/0.0.4/docs/api/pdm/batch-fetch-planar-model',
                component: ComponentCreator('/manycoreapi-demo/0.0.4/docs/api/pdm/batch-fetch-planar-model', 'c27'),
                exact: true,
                sidebar: "pdmSidebar"
              },
              {
                path: '/manycoreapi-demo/0.0.4/docs/api/pdm/batch-fetch-planar-model-build-result',
                component: ComponentCreator('/manycoreapi-demo/0.0.4/docs/api/pdm/batch-fetch-planar-model-build-result', '1f0'),
                exact: true,
                sidebar: "pdmSidebar"
              },
              {
                path: '/manycoreapi-demo/0.0.4/docs/api/pdm/batch-save-planar-model-v-2',
                component: ComponentCreator('/manycoreapi-demo/0.0.4/docs/api/pdm/batch-save-planar-model-v-2', 'ad1'),
                exact: true,
                sidebar: "pdmSidebar"
              },
              {
                path: '/manycoreapi-demo/0.0.4/docs/api/pdm/delete-planar-model',
                component: ComponentCreator('/manycoreapi-demo/0.0.4/docs/api/pdm/delete-planar-model', '632'),
                exact: true,
                sidebar: "pdmSidebar"
              },
              {
                path: '/manycoreapi-demo/0.0.4/docs/api/pdm/fetch-planar-model',
                component: ComponentCreator('/manycoreapi-demo/0.0.4/docs/api/pdm/fetch-planar-model', '75f'),
                exact: true,
                sidebar: "pdmSidebar"
              },
              {
                path: '/manycoreapi-demo/0.0.4/docs/api/pdm/fetch-planar-model-list',
                component: ComponentCreator('/manycoreapi-demo/0.0.4/docs/api/pdm/fetch-planar-model-list', '086'),
                exact: true,
                sidebar: "pdmSidebar"
              },
              {
                path: '/manycoreapi-demo/0.0.4/docs/api/pdm/save-planar-model-v-2',
                component: ComponentCreator('/manycoreapi-demo/0.0.4/docs/api/pdm/save-planar-model-v-2', '550'),
                exact: true,
                sidebar: "pdmSidebar"
              },
              {
                path: '/manycoreapi-demo/0.0.4/docs/api/pdm/平面造型设计api',
                component: ComponentCreator('/manycoreapi-demo/0.0.4/docs/api/pdm/平面造型设计api', '372'),
                exact: true,
                sidebar: "pdmSidebar"
              },
              {
                path: '/manycoreapi-demo/0.0.4/docs/api/pdm/平面造型设计管理接口',
                component: ComponentCreator('/manycoreapi-demo/0.0.4/docs/api/pdm/平面造型设计管理接口', '88b'),
                exact: true,
                sidebar: "pdmSidebar"
              },
              {
                path: '/manycoreapi-demo/0.0.4/docs/getting-started/authentication',
                component: ComponentCreator('/manycoreapi-demo/0.0.4/docs/getting-started/authentication', '210'),
                exact: true,
                sidebar: "tutorialSidebar"
              },
              {
                path: '/manycoreapi-demo/0.0.4/docs/getting-started/error-handling',
                component: ComponentCreator('/manycoreapi-demo/0.0.4/docs/getting-started/error-handling', 'a2e'),
                exact: true,
                sidebar: "tutorialSidebar"
              },
              {
                path: '/manycoreapi-demo/0.0.4/docs/getting-started/making-requests',
                component: ComponentCreator('/manycoreapi-demo/0.0.4/docs/getting-started/making-requests', '0ea'),
                exact: true,
                sidebar: "tutorialSidebar"
              },
              {
                path: '/manycoreapi-demo/0.0.4/docs/guides/best-practices',
                component: ComponentCreator('/manycoreapi-demo/0.0.4/docs/guides/best-practices', '826'),
                exact: true,
                sidebar: "tutorialSidebar"
              },
              {
                path: '/manycoreapi-demo/0.0.4/docs/guides/rate-limiting',
                component: ComponentCreator('/manycoreapi-demo/0.0.4/docs/guides/rate-limiting', '4c7'),
                exact: true,
                sidebar: "tutorialSidebar"
              },
              {
                path: '/manycoreapi-demo/0.0.4/docs/guides/sdks',
                component: ComponentCreator('/manycoreapi-demo/0.0.4/docs/guides/sdks', '3c5'),
                exact: true,
                sidebar: "tutorialSidebar"
              },
              {
                path: '/manycoreapi-demo/0.0.4/docs/guides/webhooks',
                component: ComponentCreator('/manycoreapi-demo/0.0.4/docs/guides/webhooks', '724'),
                exact: true,
                sidebar: "tutorialSidebar"
              }
            ]
          }
        ]
      }
    ]
  },
  {
    path: '/manycoreapi-demo/0.0.4/',
    component: ComponentCreator('/manycoreapi-demo/0.0.4/', '204'),
    exact: true
  },
  {
    path: '*',
    component: ComponentCreator('*'),
  },
];


