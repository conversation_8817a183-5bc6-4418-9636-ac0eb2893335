import type {SidebarsConfig} from '@docusaurus/plugin-content-docs';

// 动态导入 OpenAPI 生成的 sidebar 配置
// 如果导入失败，使用空的配置作为fallback
let cameraSidebar: any[] = [];
try {
  const sidebarModule = require('./docs/api/camera/sidebar');
  cameraSidebar = sidebarModule.default || sidebarModule.apisidebar || sidebarModule || [];
} catch (e) {
  console.warn('无法加载 camera sidebar:', e.message);
}

let doorwindowSidebar: any[] = [];
try {
  const sidebarModule = require('./docs/api/doorwindow/sidebar');
  doorwindowSidebar = sidebarModule.default || sidebarModule.apisidebar || sidebarModule || [];
} catch (e) {
  console.warn('无法加载 doorwindow sidebar:', e.message);
}

let furnitureSidebar: any[] = [];
try {
  const sidebarModule = require('./docs/api/furniture/sidebar');
  furnitureSidebar = sidebarModule.default || sidebarModule.apisidebar || sidebarModule || [];
} catch (e) {
  console.warn('无法加载 furniture sidebar:', e.message);
}

let kooluxSidebar: any[] = [];
try {
  const sidebarModule = require('./docs/api/koolux/sidebar');
  kooluxSidebar = sidebarModule.default || sidebarModule.apisidebar || sidebarModule || [];
} catch (e) {
  console.warn('无法加载 koolux sidebar:', e.message);
}

let layoutSidebar: any[] = [];
try {
  const sidebarModule = require('./docs/api/layout/sidebar');
  layoutSidebar = sidebarModule.default || sidebarModule.apisidebar || sidebarModule || [];
} catch (e) {
  console.warn('无法加载 layout sidebar:', e.message);
}

let pdmSidebar: any[] = [];
try {
  const sidebarModule = require('./docs/api/pdm/sidebar');
  pdmSidebar = sidebarModule.default || sidebarModule.apisidebar || sidebarModule || [];
} catch (e) {
  console.warn('无法加载 pdm sidebar:', e.message);
}

const sidebars: SidebarsConfig = {
  // 开发指南侧边栏
  tutorialSidebar: [
    'intro',
    {
      type: 'category',
      label: '快速开始',
      items: [
        'getting-started/authentication',
        'getting-started/making-requests',
        'getting-started/error-handling',
      ],
    },
    {
      type: 'category',
      label: '开发指南',
      items: [
        'guides/best-practices',
        'guides/rate-limiting',
        'guides/webhooks',
        'guides/sdks',
      ],
    },
    // 示例代码部分已移除
    // {
    //   type: 'category',
    //   label: '示例代码',
    //   items: [
    //     'examples/javascript',
    //     // 'examples/python',
    //     // 'examples/java',
    //     // 'examples/curl',
    //   ],
    // },
  ],

  // 各个服务的独立侧边栏（直接使用 OpenAPI 生成的内容）
  cameraSidebar: cameraSidebar,
  doorwindowSidebar: doorwindowSidebar,
  furnitureSidebar: furnitureSidebar,
  kooluxSidebar: kooluxSidebar,
  layoutSidebar: layoutSidebar,
  pdmSidebar: pdmSidebar,
};

export default sidebars;