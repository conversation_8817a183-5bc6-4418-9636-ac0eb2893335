{{>licenseInfo}}

package {{invokerPackage}}.auth;

import org.springframework.http.HttpHeaders;
import org.springframework.util.MultiValueMap;
import java.util.Map;
import java.util.List;
import java.security.MessageDigest;
import java.security.NoSuchAlgorithmException;
import java.math.BigInteger;

{{>generatedAnnotation}}
public class ApiKeyAuth implements Authentication {
    private final String location;
    private final String paramName;

    private String apiKey;
    private String apiKeyPrefix;

    private String appKey;
    private String appSecret;
    private String appUid;

    public ApiKeyAuth(String location, String paramName) {
        this.location = location;
        this.paramName = paramName;
    }

    public String getLocation() {
        return location;
    }

    public String getParamName() {
        return paramName;
    }

    public String getApiKey() {
        return apiKey;
    }

    public void setApiKey(String apiKey) {
        this.apiKey = apiKey;
    }

    public String getApiKeyPrefix() {
        return apiKeyPrefix;
    }

    public void setApiKeyPrefix(String apiKeyPrefix) {
        this.apiKeyPrefix = apiKeyPrefix;
    }

    public String getAppKey() {
        return appKey;
    }

    public void setAppKey(String appKey) {
        this.appKey = appKey;
    }

    public String getAppSecret() {
        return appSecret;
    }

    public void setAppSecret(String appSecret) {
        this.appSecret = appSecret;
    }

    public String getAppUid() {
        return appUid;
    }

    public void setAppUid(String appUid) {
        this.appUid = appUid;
    }

    /**
     * 设置认证凭据
     * @param appKey 应用密钥
     * @param appSecret 应用密钥
     * @param appUid 用户ID
     */
    public void setAuthCredentials(String appKey, String appSecret, String appUid) {
        this.appKey = appKey;
        this.appSecret = appSecret;
        this.appUid = appUid;
    }

    @Override
    public void applyToParams(MultiValueMap<String, String> queryParams, HttpHeaders headerParams, MultiValueMap<String, String> cookieParams) {
        // 如果设置了认证参数，则计算并添加认证参数
        if (appKey != null && appSecret != null && appUid != null) {
            // 获取当前时间戳
            String timestamp = String.valueOf(System.currentTimeMillis());

            // 计算签名: appSecret + appKey + appUid + timestamp
            String sign = calculateMD5Hash(appSecret + appKey + appUid + timestamp);

            // 添加所有认证参数到请求中
            queryParams.add("appkey", appKey);
            queryParams.add("appuid", appUid);
            queryParams.add("timestamp", timestamp);
            queryParams.add("sign", sign);
        }
        // 原始ApiKey逻辑
        else if (apiKey != null) {
            String value;
            if (apiKeyPrefix != null) {
                value = apiKeyPrefix + " " + apiKey;
            } else {
                value = apiKey;
            }
            if ("query".equals(location)) {
                queryParams.add(paramName, value);
            } else if ("header".equals(location)) {
                headerParams.add(paramName, value);
            } else if ("cookie".equals(location)) {
                cookieParams.add(paramName, value);
            }
        }
    }

    /**
     * 计算字符串的MD5哈希值
     * @param input 输入字符串
     * @return MD5哈希值（十六进制字符串）
     */
    private String calculateMD5Hash(String input) {
        try {
            MessageDigest md = MessageDigest.getInstance("MD5");
            byte[] messageDigest = md.digest(input.getBytes());

            // 转换为十六进制字符串
            BigInteger no = new BigInteger(1, messageDigest);
            String hashText = no.toString(16);

            // 填充零以确保长度为32位
            while (hashText.length() < 32) {
                hashText = "0" + hashText;
            }

            return hashText;
        } catch (NoSuchAlgorithmException e) {
            throw new RuntimeException("MD5哈希计算失败", e);
        }
    }
}
