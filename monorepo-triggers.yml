---
# This file contains triggers for child pipelines in the monorepo.

workflow:
  rules:
    - if: '$CI_PIPELINE_SOURCE == "merge_request_event"'
    - if: '$CI_COMMIT_BRANCH'
    - if: '$CI_COMMIT_TAG'

# stages 定义已移动到主 .gitlab-ci.yml 文件中

trigger_backend_apis:
  stage: trigger
  trigger:
    include: 'backend-apis/.gitlab-ci.yml'
    strategy: depend
  rules:
    - changes:
        - backend-apis/**/*

trigger_frontend_idp:
  stage: trigger
  trigger:
    include: 'frontend-apis/idp/.gitlab-ci.yml'
    strategy: depend
  rules:
    - changes:
        - frontend-apis/idp/**/* 

trigger_frontend_sdk:
  stage: trigger
  trigger:
    include: 'frontend-apis/sdk/.gitlab-ci.yml'
    strategy: depend
  rules:
    - changes:
        - frontend-apis/sdk/**/*         


trigger_documentation:
  stage: trigger
  trigger:
    include: 'documentation/.gitlab-ci.yml'
    strategy: depend
  rules:
    - changes:
        - documentation/**/*         