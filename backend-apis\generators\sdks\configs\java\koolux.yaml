'!include': '../../shared/shared-data-mappings.yaml'
generatorName: "java"
inputSpec: "specifications/services/koolux/openapi.yaml"
outputDir: "build/sdks/koolux/java"
templateDir: "generators/sdks/templates/java"
additionalProperties:
  groupId: "com.manycore"
  artifactId: "koolux-rest-client"
  artifactVersion: "0.0.1-SNAPSHOT"
  modelPackage: "com.manycore.koolux.client.model"
  apiPackage: "com.manycore.koolux.client.api"
  invokerPackage: "com.manycore.koolux.client"
  java8: true
  dateLibrary: "java8"
  library: "resttemplate"
  sourceFolder: "src/main/java"
  hideGenerationTimestamp: true
  serializationLibrary: "jackson"
  useRuntimeException: false

    

