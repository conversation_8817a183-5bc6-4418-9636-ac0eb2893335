# 🚀 Docusaurus 文档部署指南

这份指南详细说明了如何部署酷家乐 API 文档站点到不同的环境。

## 📋 部署流程概览

```mermaid
graph LR
    A[OpenAPI 变更] --> B[MR 创建]
    B --> C[自动构建预览]
    C --> D[预览环境部署]
    D --> E[评审通过]
    E --> F[合并到 master]
    F --> G[生产环境部署]
    G --> H[GitLab Pages]
```

## 🔧 环境配置

### 开发环境

```bash
# 1. 克隆仓库
git clone <your-repo-url>
cd backend-api-sdk

# 2. 初始化文档环境
chmod +x scripts/setup-docs.sh
./scripts/setup-docs.sh

# 3. 启动开发服务器
cd docs-site
npm start
```

### 预览环境 (MR)

每个 Merge Request 会自动：

1. **检测变更**: 自动检测 OpenAPI 文件变更
2. **构建文档**: 生成最新的 API 文档
3. **部署预览**: 部署到临时预览环境
4. **提供链接**: 在 MR 中提供预览链接

#### 预览环境配置

```yaml
# .gitlab-ci.yml 中的预览配置
docs_preview:
  stage: docs
  script:
    - ./scripts/ci/docs/preview-docusaurus.sh
  environment:
    name: review/mr-$CI_MERGE_REQUEST_IID
    url: $CI_PROJECT_URL/-/jobs/$CI_JOB_ID/artifacts/public/index.html
```

### 生产环境

主分支 (`master`/`main`) 合并后自动部署到 GitLab Pages。

#### 生产环境访问

- **内部访问**: `https://your-company.gitlab.io/backend-api-sdk/`
- **外网发布**: 配置自定义域名

## 🛠️ 自定义域名配置

### 配置 GitLab Pages 自定义域名

1. 在 GitLab 项目设置中配置 Pages 域名
2. 添加 DNS 记录
3. 配置 SSL 证书

```bash
# 在 docs-site/static/ 目录下创建 CNAME 文件
echo "docs.kujiale.com" > docs-site/static/CNAME
```

### 更新 Docusaurus 配置

```typescript
// docs-site/docusaurus.config.ts
const config: Config = {
  url: 'https://docs.kujiale.com', // 自定义域名
  baseUrl: '/',
  // ...
};
```

## 🔐 访问控制策略

### 内部评审环境

```yaml
# 内部访问限制
docs_preview:
  environment:
    name: review/mr-$CI_MERGE_REQUEST_IID
  rules:
    - if: '$CI_MERGE_REQUEST_IID'
  # 只有项目成员可以访问
```

### 外网发布环境

```yaml
# 外网发布配置
pages:
  rules:
    - if: '$CI_COMMIT_BRANCH == "master"'
  # 公开访问
```

### 分级发布策略

```mermaid
graph TD
    A[OpenAPI 变更] --> B[内部预览环境]
    B --> C{评审通过?}
    C -->|Yes| D[内部生产环境]
    C -->|No| E[修改后重新预览]
    D --> F{外网发布?}
    F -->|Yes| G[外网生产环境]
    F -->|No| H[仅内部可用]
```

## 🚀 CI/CD 流水线详解

### 完整流水线

```yaml
stages:
  - setup        # 环境检查
  - validate     # API 规范验证
  - docs         # 文档生成
  - review       # 评审管理
  - deploy       # 部署发布
```

### 关键 Jobs

#### 1. 文档预览 (docs_preview)

```bash
# 执行脚本
scripts/ci/docs/preview-docusaurus.sh

# 主要功能
- 检测 API 变更
- 生成 Docusaurus 站点
- 部署到预览环境
- 生成预览信息页面
```

#### 2. 生产构建 (build_docs)

```bash
# 执行脚本
scripts/ci/docs/build-docusaurus.sh

# 主要功能
- 安装依赖
- 生成所有 API 文档
- 构建生产版本
- 优化静态资源
```

#### 3. 部署发布 (pages)

```bash
# GitLab Pages 自动部署
- 使用 build_docs 的产物
- 部署到 GitLab Pages
- 更新生产环境
```

## 🔍 监控和调试

### 构建日志

```bash
# 查看构建详情
- 访问 GitLab CI/CD -> Jobs
- 查看具体 Job 的执行日志
- 检查错误信息和性能指标
```

### 常见问题

#### 1. Node.js 版本问题

```bash
# 确保使用 Node.js 18.0+
node --version

# 在 CI 中检查
before_script:
  - node --version
  - npm --version
```

#### 2. OpenAPI 文件路径问题

```bash
# 检查文件是否存在
SPEC_FILES=(
    "../openapi/diymodeldw-service/restapi.yaml"
    # ...
)

for spec_file in "${SPEC_FILES[@]}"; do
    if [ ! -f "$spec_file" ]; then
        echo "❌ OpenAPI 文件不存在: $spec_file"
        exit 1
    fi
done
```

#### 3. 构建失败

```bash
# 清理缓存
npm run clear

# 重新生成 API 文档
npm run clean-api-docs all
npm run gen-api-docs all

# 重新构建
npm run build
```

## 📊 性能优化

### 构建优化

```typescript
// docusaurus.config.ts
export default {
  // 启用代码分割
  onBrokenLinks: 'throw',
  onBrokenMarkdownLinks: 'warn',
  
  // 优化构建
  future: {
    experimental_faster: true,
  },
};
```

### 缓存策略

```yaml
# .gitlab-ci.yml
cache:
  key: "$CI_COMMIT_REF_SLUG"
  paths:
    - docs-site/node_modules/
    - docs-site/.docusaurus/
```

### 静态资源优化

```bash
# 图片压缩
npm install --save-dev imagemin-cli

# CSS 压缩
npm install --save-dev cssnano

# JS 压缩 (Docusaurus 内置)
```

## 🌐 多环境部署

### 开发环境
- **地址**: `http://localhost:3000`
- **用途**: 本地开发和测试

### 预览环境
- **地址**: GitLab CI 临时链接
- **用途**: MR 评审和测试

### 预生产环境
- **地址**: `https://staging-docs.kujiale.com`
- **用途**: 内部验证

### 生产环境
- **地址**: `https://docs.kujiale.com`
- **用途**: 外网正式发布

## 🔐 安全考虑

### API 密钥保护

```yaml
# 在 GitLab CI/CD Variables 中设置敏感信息
variables:
  ALGOLIA_APP_ID: $ALGOLIA_APP_ID
  ALGOLIA_API_KEY: $ALGOLIA_API_KEY
```

### 访问控制

```typescript
// 条件渲染敏感内容
{process.env.NODE_ENV === 'production' && (
  <div>仅生产环境可见的内容</div>
)}
```

## 📝 维护建议

### 定期任务

1. **更新依赖**: 每月更新 npm 包
2. **检查链接**: 定期检查外部链接有效性
3. **性能监控**: 监控构建时间和站点性能
4. **内容审核**: 定期审核文档内容的准确性

### 最佳实践

1. **版本控制**: 所有配置文件都纳入版本控制
2. **自动化测试**: 编写文档构建的自动化测试
3. **监控告警**: 设置构建失败的告警机制
4. **备份策略**: 定期备份重要的配置和内容

---

如有问题，请参考 [故障排除指南](./TROUBLESHOOTING.md) 或联系开发团队。 