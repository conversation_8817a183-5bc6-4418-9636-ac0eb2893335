# API 评审流程文档

## 1. 概述

本文档详细说明了基于 GitLab MR 和 CI/CD 的 API 评审流程。该流程确保所有 API 变更都经过严格的规范校验、文档生成和人工评审，保障 API 的质量和一致性。

## 2. 流程架构

```
业务方 → 创建 MR → CI 自动校验 → 生成Manual预览 → 自动评论通知 → API 小组评审 → 合并 MR → 自动生成 SDK
```

## 3. 参与角色

### 3.1 业务方（API 设计者/开发者）
- 负责编写和修改 OpenAPI 规范文件 (`restapi.yaml`)
- 创建和维护 MR
- 根据评审反馈修改 API 定义，resolve comment

### 3.2 后端 API 小组（评审者）
- 审核 API 设计的合理性、规范性、安全性
- 在 MR 中通过 comment 提供详细的评审意见
- 使用提供的评审检查清单进行系统性评审

### 3.3 CI/CD 系统
- 自动执行规范校验
- 生成 Manual 网站文档预览
- 自动在 MR 中添加预览链接和评审检查清单
- 构建和部署 SDK

## 4. 详细流程步骤

### 4.1 准备阶段

1. **创建特性分支**
   ```bash
   git checkout master
   git pull origin master
   git checkout -b feature/add-user-management-api
   ```

2. **编写/修改 API 定义**
   - 编辑对应服务的 `openapi/<service-name>/restapi.yaml` 文件
   - 确保遵循 OpenAPI 3.x 规范
   - 更新相关的配置文件（如需要）

### 4.2 提交评审

1. **推送分支并创建 MR**
   ```bash
   git add .
   git commit -m "feat: 新增用户管理 API"
   git push origin feature/add-user-management-api
   ```

2. **创建 Merge Request**
   - 目标分支：`develop`
   - 标题格式：`[API预审] 新增用户管理接口` 或 `[API终审] 优化订单创建接口v2`
   - 描述中包含：
     - 变更背景和目的
     - 主要 API 变更点
     - 需要特别关注的设计决策

3. **设置初始标签**
   - `API-Review::Pending-Pre-Review`（设计阶段）
   - `API-Review::Pending-Final-Review`（开发完成阶段）

### 4.3 CI 自动化处理

MR 创建后，CI 流水线自动执行以下步骤：

#### Stage 1: validate_api_changes
- 检测 API 变更（检查 `openapi/` 目录下的 YAML 文件）
- 设置 `HAS_API_CHANGES` 变量决定后续流程是否执行

#### Stage 2: validate_specs
- 对每个变更的 `restapi.yaml` 文件执行 Spectral 校验
- 检查 OpenAPI 规范的语法和最佳实践
- 校验失败会阻止流水线继续

#### Stage 3: 文档生成和部署流程

##### 3.1 generate_all_dynamic_content
- 动态生成 OpenAPI 配置文件
- 生成 Docusaurus 侧边栏配置
- 生成 API 文档内容和链接

##### 3.2 docs_preview (手动触发)
- 部署到开发环境预览
- 生成预览链接：`https://manual.qunhequnhe.com/manycoreapi-demo/{version}/`

##### 3.3 deploy_manual_* 系列任务
- **deploy_manual_dev**: 部署到开发环境
- **deploy_manual_staging**: 部署到预发布环境  
- **deploy_manual_prod**: 部署到生产环境

#### Stage 4: add_mr_comment
- **自动检测 API 变更**：只有当 `HAS_API_CHANGES=true` 时才执行
- **生成预览链接**：基于分支类型生成不同的 Manual 网站链接
- **自动添加 MR 评论**，包含：
  - 完整文档预览链接
  - 具体 API 服务的文档链接（如 `designinfoservice`、`furniture-design-service` 等）
  - 变更文件列表
  - **标准化评审检查清单**
  - 部署环境信息

### 4.4 自动 MR 评论功能

当检测到 API 变更时，系统会自动在 MR 中添加包含以下内容的评论：

```markdown
## 📖 API 文档预览已生成

**🌐 完整文档预览：** [查看Manual网站](预览链接)

**📋 本次变更的API文档：**
- [📖 designinfoservice API](具体服务链接)
- [📖 furniture-design-service API](具体服务链接)

**📄 变更文件列表：**
- `openapi/designinfoservice/restapi.yaml`
- `openapi/furniture-design-service/restapi.yaml`

**📋 评审检查清单：**
- [ ] API 设计符合 RESTful 原则
- [ ] 新增/修改的端点文档完整
- [ ] 请求/响应参数说明清晰
- [ ] 错误响应格式符合标准
- [ ] 示例代码准确有效
- [ ] 安全性考虑充分（认证、授权、数据验证）
- [ ] 向后兼容性评估完成
- [ ] 性能影响评估（如有必要）
```

### 4.5 Manual 网站预览

根据不同的分支和环境，生成不同的预览地址：

| 分支类型 | 预览环境 | 访问地址格式 |
|----------|----------|--------------|
| 特性分支 | 开发环境 | `https://manual.qunhequnhe.com/manycoreapi-demo/0.0.1-{branch}-{commit}/` |
| master/main | 预发布环境 | `https://manual.qunhequnhe.com/manycoreapi-staging/1.0.0-{timestamp}/` |
| Tag 版本 | 生产环境 | `https://manual.qunhequnhe.com/manycoreapi/{tag}/` |

### 4.6 API 小组评审

1. **接收通知**
   - 通过 GitLab MR 自动评论获得预览链接
   - GitLab 通知或定期检查待评审的 MR

2. **执行评审**
   - 点击 MR 评论中的 Manual 网站预览链接
   - 查看完整的 API 文档和具体服务文档
   - 使用提供的评审检查清单进行系统性评审
   - 重点关注：
     - API 设计的合理性和一致性
     - 安全性考虑
     - 向后兼容性
     - 文档完整性
     - 错误处理

3. **提供反馈**
   - 在 MR 评论区详细记录评审意见
   - 在评审检查清单中勾选已完成的项目
   - 对于需要修改的地方，提供具体的改进建议
   - 更新 MR 标签：
     - `API-Review::Changes-Requested`：需要修改
     - `API-Review::Approved`：评审通过

### 4.7 迭代修改（如需要）

1. **业务方修改**
   - 根据评审反馈修改 `restapi.yaml`
   - 提交新的 commit

2. **重新触发 CI**
   - CI 自动重新执行校验和文档生成
   - 重新部署 Manual 网站预览
   - 自动更新 MR 评论中的预览链接

3. **再次评审**
   - API 小组审核修改内容
   - 重复直到评审通过

### 4.8 评审通过与合并

1. **最终确认**
   - API 小组将标签更新为 `API-Review::Approved`
   - 在评审检查清单中确认所有项目

2. **合并 MR**
   - 具有合并权限的成员合并 MR 到 `develop` 分支

### 4.9 自动化 SDK 生成

MR 合并到 `develop` 或 `master` 分支后：

1. **SDK 生成**（`generate_sdk` stage）
   - 使用 OpenAPI Generator 生成 Java 和 TypeScript SDK
   - 为每个服务生成独立的 SDK

2. **构建和测试**（`build` 和 `test` stages）
   - 编译生成的 SDK 代码
   - 运行单元测试

3. **部署快照版本**（`deploy` stage）
   - 将 SNAPSHOT 版本部署到 Maven 仓库
   - 供开发环境使用

## 5. GitLab 标签规范

| 标签 | 含义 | 使用时机 |
|------|------|----------|
| `API-Review::Pending-Pre-Review` | 等待预审 | MR 创建时（设计阶段） |
| `API-Review::Pending-Final-Review` | 等待终审 | 开发完成后 |
| `API-Review::Changes-Requested` | 需要修改 | 评审发现问题时 |
| `API-Review::Approved` | 评审通过 | 评审完成且无问题时 |
| `API-Review::On-Hold` | 评审暂停 | 等待外部依赖时 |

## 6. 文档预览访问方式

### 6.1 Manual 网站预览（推荐）
- **主要方式**：点击 MR 自动评论中的预览链接
- **特点**：
  - 完整的在线文档体验
  - 支持交互式 API 测试
  - 响应式设计，支持移动端访问
  - 实时同步最新变更

### 6.2 GitLab Environments
- 在 MR 页面查看 "Environments" 部分
- 查看部署状态和访问链接

### 6.3 GitLab Pages（备用）
- 进入 MR 页面的 "Pipelines" 标签
- 找到对应的流水线
- 查看 `pages` job 的部署链接

## 7. 评审检查清单详解

### 7.1 API 设计规范
- [ ] **RESTful 原则**：使用正确的 HTTP 方法和状态码
- [ ] **端点命名**：使用清晰、一致的资源命名
- [ ] **版本管理**：合理的 API 版本策略

### 7.2 文档完整性
- [ ] **端点文档**：每个端点都有完整的描述
- [ ] **参数说明**：请求/响应参数类型、格式、约束说明清晰
- [ ] **示例代码**：提供准确的请求/响应示例

### 7.3 安全性考虑
- [ ] **认证授权**：明确的身份验证和权限控制
- [ ] **数据验证**：输入参数的合理验证规则
- [ ] **敏感信息**：避免在响应中暴露敏感数据

### 7.4 兼容性和性能
- [ ] **向后兼容**：新版本不破坏现有客户端
- [ ] **性能影响**：评估 API 变更对系统性能的影响

## 8. 常见问题处理

### 8.1 Spectral 校验失败
- 检查 OpenAPI 规范语法
- 确保遵循最佳实践
- 参考 Spectral 错误信息进行修复

### 8.2 Manual 网站预览失败
- 检查 CI 流水线中的 `generate_docs_content` 和 `generate_openapi_configs` 步骤
- 确保 OpenAPI 文件格式正确
- 验证动态内容生成是否成功

### 8.3 自动评论未生成
- 确认 `HAS_API_CHANGES` 变量设置正确
- 检查 `GITLAB_TOKEN` 是否配置
- 验证 API 文件路径是否符合检测规则

### 8.4 文档链接无法访问
- 检查 Manual 网站的部署状态
- 确认预览版本号生成逻辑
- 验证 Docusaurus 构建是否成功

## 9. 最佳实践

### 9.1 API 设计
- 遵循 RESTful 设计原则
- 使用清晰的命名约定
- 提供完整的错误响应定义
- 包含详细的描述和示例

### 9.2 MR 管理
- 使用描述性的 MR 标题
- 在描述中提供充分的上下文
- 及时响应评审反馈
- 保持 MR 的范围适中

### 9.3 评审质量
- 使用提供的评审检查清单
- 关注 API 的长期维护性
- 考虑向后兼容性影响
- 验证安全性设计
- 确保文档的完整性和准确性

### 9.4 文档预览
- 优先使用 Manual 网站预览进行评审
- 测试交互式 API 文档功能
- 验证文档在不同设备上的显示效果

## 10. 技术实现细节

### 10.1 动态内容生成
- **OpenAPI 配置生成**：`scripts/generate-openapi-configs.js`
- **文档内容生成**：`scripts/generate-docs-content.js`
- **侧边栏配置**：动态生成 `docs-site/sidebars.ts`

### 10.2 CI/CD 流程
- **API 变更检测**：`scripts/ci/validation/detect-api-changes.sh`
- **Manual 部署**：`scripts/ci/docs/deploy-to-manual.sh`
- **MR 评论**：`scripts/add-mr-comment.sh`

### 10.3 环境配置
- **开发环境**：`manycoreapi-demo`
- **预发布环境**：`manycoreapi-staging`
- **生产环境**：`manycoreapi`

## 11. 工具和技术栈

- **OpenAPI 规范**: 3.x (YAML 格式)
- **规范校验**: Stoplight Spectral
- **文档生成**: Docusaurus + Stoplight Elements
- **SDK 生成**: OpenAPI Generator
- **CI/CD**: GitLab CI/CD
- **版本控制**: Git + GitLab
- **文档托管**: Manual 网站（基于 Docusaurus）

## 12. 联系方式

如有问题或建议，请联系：
- 后端 API 小组：[联系方式]
- CI/CD 支持：[联系方式]
- Manual 网站技术支持：[联系方式] 