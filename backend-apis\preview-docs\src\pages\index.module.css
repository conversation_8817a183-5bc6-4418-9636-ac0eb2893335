/* 英雄区域 */
.heroBanner {
  background: linear-gradient(135deg, #3b82f6 0%, #8b5cf6 50%, #6366f1 100%);
  padding: 6rem 0;
  color: white;
  position: relative;
  overflow: hidden;
}

.heroBanner::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: 
    radial-gradient(circle at 20% 80%, rgba(120, 119, 198, 0.3) 0%, transparent 50%),
    radial-gradient(circle at 80% 20%, rgba(139, 92, 246, 0.3) 0%, transparent 50%),
    radial-gradient(circle at 40% 40%, rgba(59, 130, 246, 0.2) 0%, transparent 50%);
  background-size: 100% 100%;
  animation: backgroundFloat 15s ease-in-out infinite alternate;
}

@keyframes backgroundFloat {
  0% { 
    transform: translateX(0px) translateY(0px) scale(1);
    opacity: 0.6;
  }
  100% { 
    transform: translateX(-10px) translateY(-5px) scale(1.02);
    opacity: 0.8;
  }
}

.heroContent {
  display: flex;
  align-items: center;
  gap: 4rem;
  max-width: 1200px;
  margin: 0 auto;
  position: relative;
  z-index: 1;
}

.heroText {
  flex: 1;
}

.heroTitle {
  font-size: 3.5rem;
  font-weight: 800;
  margin-bottom: 1rem;
  line-height: 1.2;
  text-shadow: 0 2px 4px rgba(0,0,0,0.3);
}

.heroSubtitle {
  display: block;
  font-size: 2rem;
  font-weight: 400;
  margin-top: 0.5rem;
  opacity: 0.9;
}

.heroDescription {
  font-size: 1.25rem;
  margin-bottom: 2rem;
  line-height: 1.6;
  opacity: 0.95;
  max-width: 600px;
}

.heroButtons {
  display: flex;
  gap: 1rem;
  flex-wrap: wrap;
}

.heroButton {
  min-width: 140px;
  font-weight: 600;
  box-shadow: 0 4px 12px rgba(0,0,0,0.2);
  transition: all 0.3s ease;
}

.heroButton:hover {
  transform: translateY(-2px);
  box-shadow: 0 6px 16px rgba(0,0,0,0.3);
}

.heroDemo {
  flex: 1;
  max-width: 600px;
}

.demoWindow {
  background: #1e1e1e;
  border-radius: 12px;
  box-shadow: 0 20px 40px rgba(0,0,0,0.3);
  overflow: hidden;
  animation: floatUp 3s ease-in-out infinite alternate;
}

@keyframes floatUp {
  0% { transform: translateY(0px); }
  100% { transform: translateY(-10px); }
}

.demoHeader {
  background: #2d2d2d;
  padding: 12px 20px;
  display: flex;
  align-items: center;
  gap: 12px;
}

.demoButtons {
  display: flex;
  gap: 8px;
}

.demoButtons span {
  width: 12px;
  height: 12px;
  border-radius: 50%;
  background: #ff5f56;
}

.demoButtons span:nth-child(2) {
  background: #ffbd2e;
}

.demoButtons span:nth-child(3) {
  background: #27ca3f;
}

.demoTitle {
  color: #ccc;
  font-size: 0.9rem;
  font-weight: 500;
}

.demoCode {
  font-size: 0.85rem;
  margin: 0;
  background: transparent !important;
}

/* 统计数据区域 */
.statsSection {
  background: linear-gradient(135deg, #f8fafc 0%, #e2e8f0 50%, #f1f5f9 100%);
  padding: 4rem 0;
  margin-top: -2rem;
  position: relative;
  z-index: 2;
}

.statsGrid {
  display: grid;
  grid-template-columns: repeat(4, 1fr);
  gap: 1.5rem;
  max-width: 1000px;
  margin: 0 auto;
}

.statCard {
  background: rgba(255, 255, 255, 0.9);
  padding: 2rem 1.5rem;
  border-radius: 16px;
  text-align: center;
  box-shadow: 0 4px 20px rgba(59, 130, 246, 0.08);
  transition: all 0.3s ease;
  border: 1px solid rgba(59, 130, 246, 0.1);
  backdrop-filter: blur(10px);
}

.statCard:hover {
  transform: translateY(-6px);
  box-shadow: 0 8px 30px rgba(59, 130, 246, 0.15);
  background: rgba(255, 255, 255, 0.95);
}

.statIcon {
  font-size: 2.5rem;
  margin-bottom: 1rem;
}

.statNumber {
  font-size: 2.5rem;
  font-weight: 800;
  background: linear-gradient(135deg, #3b82f6, #8b5cf6);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
  margin-bottom: 0.5rem;
}

.statLabel {
  color: #64748b;
  font-weight: 500;
  font-size: 1rem;
}

/* 功能特性区域 */
.features {
  padding: 6rem 0;
  background: white;
}

.sectionHeader {
  text-align: center;
  margin-bottom: 4rem;
}

.sectionTitle {
  font-size: 2.5rem;
  font-weight: 700;
  color: #2e3440;
  margin-bottom: 1rem;
}

.sectionDescription {
  font-size: 1.25rem;
  color: #64748b;
  max-width: 600px;
  margin: 0 auto;
  line-height: 1.6;
}

.feature {
  margin-bottom: 2rem;
}

.featureCard {
  background: rgba(255, 255, 255, 0.95);
  padding: 2.5rem 2rem;
  border-radius: 16px;
  height: 100%;
  box-shadow: 0 4px 20px rgba(59, 130, 246, 0.06);
  border: 1px solid rgba(59, 130, 246, 0.08);
  transition: all 0.3s ease;
  position: relative;
  overflow: hidden;
  backdrop-filter: blur(20px);
}

.featureCard::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  height: 4px;
  background: linear-gradient(90deg, #3b82f6, #8b5cf6, #6366f1);
  transform: scaleX(0);
  transition: transform 0.3s ease;
}

.featureCard:hover {
  transform: translateY(-6px);
  box-shadow: 0 12px 40px rgba(59, 130, 246, 0.15);
  background: rgba(255, 255, 255, 1);
}

.featureCard:hover::before {
  transform: scaleX(1);
}

.featureTitle {
  font-size: 1.5rem;
  font-weight: 600;
  color: #2e3440;
  margin-bottom: 1rem;
}

.featureDescription {
  color: #64748b;
  line-height: 1.6;
  font-size: 1rem;
}

/* 快速开始区域 */
.quickStart {
  background: linear-gradient(135deg, #3b82f6 0%, #8b5cf6 50%, #6366f1 100%);
  padding: 6rem 0;
  color: white;
  position: relative;
  overflow: hidden;
}

.quickStart::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: 
    radial-gradient(circle at 70% 20%, rgba(139, 92, 246, 0.3) 0%, transparent 50%),
    radial-gradient(circle at 30% 80%, rgba(99, 102, 241, 0.3) 0%, transparent 50%);
  animation: backgroundFloat 20s ease-in-out infinite alternate-reverse;
}

.quickStart .sectionTitle,
.quickStart .sectionDescription {
  color: white;
}

.quickStartGrid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
  gap: 2rem;
  margin-top: 3rem;
}

.quickStartStep {
  background: rgba(255, 255, 255, 0.15);
  padding: 2.5rem 2rem;
  border-radius: 16px;
  text-align: center;
  backdrop-filter: blur(20px);
  border: 1px solid rgba(255, 255, 255, 0.2);
  transition: all 0.3s ease;
  position: relative;
  z-index: 1;
}

.quickStartStep:hover {
  background: rgba(255, 255, 255, 0.2);
  transform: translateY(-6px);
  box-shadow: 0 12px 40px rgba(0, 0, 0, 0.2);
}

.stepNumber {
  width: 60px;
  height: 60px;
  border-radius: 50%;
  background: linear-gradient(135deg, rgba(255, 255, 255, 0.25), rgba(255, 255, 255, 0.1));
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 1.5rem;
  font-weight: 700;
  margin: 0 auto 1.5rem;
  border: 2px solid rgba(255, 255, 255, 0.3);
  backdrop-filter: blur(10px);
}

.quickStartStep h3 {
  font-size: 1.5rem;
  margin-bottom: 1rem;
  color: white;
}

.quickStartStep p {
  margin-bottom: 1.5rem;
  opacity: 0.9;
  line-height: 1.6;
}

/* 客户信任区域 */
.trustedBy {
  padding: 6rem 0;
  background: linear-gradient(135deg, #f8fafc 0%, #e2e8f0 100%);
}

.customersGrid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(150px, 1fr));
  gap: 2rem;
  margin-top: 3rem;
  max-width: 800px;
  margin-left: auto;
  margin-right: auto;
}

.customerCard {
  background: rgba(255, 255, 255, 0.9);
  padding: 2rem 1rem;
  border-radius: 12px;
  text-align: center;
  box-shadow: 0 4px 16px rgba(59, 130, 246, 0.08);
  transition: all 0.3s ease;
  border: 1px solid rgba(59, 130, 246, 0.1);
  backdrop-filter: blur(10px);
}

.customerCard:hover {
  transform: translateY(-4px);
  box-shadow: 0 8px 24px rgba(59, 130, 246, 0.15);
  background: rgba(255, 255, 255, 1);
}

.customerLogo {
  font-size: 2.5rem;
  margin-bottom: 1rem;
}

.customerName {
  font-weight: 600;
  color: #2e3440;
  font-size: 0.95rem;
}

/* CTA 区域 */
.cta {
  background: linear-gradient(135deg, #1e293b 0%, #334155 50%, #0f172a 100%);
  padding: 6rem 0;
  color: white;
  text-align: center;
  position: relative;
  overflow: hidden;
}

.cta::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: 
    radial-gradient(circle at 30% 30%, rgba(59, 130, 246, 0.2) 0%, transparent 50%),
    radial-gradient(circle at 70% 70%, rgba(139, 92, 246, 0.2) 0%, transparent 50%);
  animation: backgroundFloat 25s ease-in-out infinite;
}

.ctaContent {
  max-width: 600px;
  margin: 0 auto;
  position: relative;
  z-index: 1;
}

.ctaTitle {
  font-size: 2.5rem;
  font-weight: 700;
  margin-bottom: 1rem;
}

.ctaDescription {
  font-size: 1.25rem;
  margin-bottom: 2rem;
  opacity: 0.9;
  line-height: 1.6;
}

.ctaButtons {
  display: flex;
  gap: 1rem;
  justify-content: center;
  flex-wrap: wrap;
}

.ctaButtons .button {
  min-width: 140px;
  font-weight: 600;
  box-shadow: 0 4px 12px rgba(0,0,0,0.2);
  transition: all 0.3s ease;
}

.ctaButtons .button:hover {
  transform: translateY(-2px);
  box-shadow: 0 6px 16px rgba(0,0,0,0.3);
}

/* 响应式设计 */
@media (max-width: 768px) {
  .heroContent {
    flex-direction: column;
    text-align: center;
    gap: 3rem;
  }
  
  .heroTitle {
    font-size: 2.5rem;
  }
  
  .heroSubtitle {
    font-size: 1.5rem;
  }
  
  .heroDescription {
    font-size: 1.1rem;
  }
  
  .heroButtons {
    justify-content: center;
  }
  
  .statsGrid {
    grid-template-columns: repeat(2, 1fr);
    gap: 1rem;
  }
  
  .sectionTitle {
    font-size: 2rem;
  }
  
  .sectionDescription {
    font-size: 1.1rem;
  }
  
  .quickStartGrid {
    grid-template-columns: 1fr;
  }
  
  .customersGrid {
    grid-template-columns: repeat(3, 1fr);
  }
  
  .ctaButtons {
    flex-direction: column;
    align-items: center;
  }
}

@media (max-width: 480px) {
  .heroBanner {
    padding: 4rem 0;
  }
  
  .heroTitle {
    font-size: 2rem;
  }
  
  .heroSubtitle {
    font-size: 1.25rem;
  }
  
  .statsGrid {
    grid-template-columns: repeat(2, 1fr);
    gap: 1rem;
  }
  
  .customersGrid {
    grid-template-columns: repeat(2, 1fr);
  }
  
  .features,
  .quickStart,
  .trustedBy,
  .cta {
    padding: 4rem 0;
  }
  
  .featureCard,
  .quickStartStep {
    padding: 2rem 1.5rem;
  }
} 