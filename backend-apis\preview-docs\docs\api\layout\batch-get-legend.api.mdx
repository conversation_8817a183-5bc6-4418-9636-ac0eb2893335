---
id: batch-get-legend
title: "批量获取图例信息"
description: "根据图例ID列表批量获取图例信息。支持异步处理，适用于大量数据查询、数据同步等场景。单次最多支持100个图例。"
sidebar_label: "批量获取图例信息"
hide_title: true
hide_table_of_contents: true
api: {"tags":["批量操作","图例管理接口"],"description":"根据图例ID列表批量获取图例信息。支持异步处理，适用于大量数据查询、数据同步等场景。单次最多支持100个图例。","operationId":"batchGetLegend","parameters":[{"name":"obsDesignId","in":"path","description":"设计方案的ID","required":true,"schema":{"type":"string"},"example":"ObQ1gR2nF8kE"},{"name":"levelId","in":"path","description":"楼层ID","required":true,"schema":{"type":"string"},"example":"level_001"}],"requestBody":{"content":{"application/json":{"schema":{"required":["batchRequests"],"type":"object","properties":{"requestId":{"type":"string"},"operationId":{"type":"string"},"batchRequests":{"type":"array","description":"要获取的图例ID列表，每个ID必须是有效的图例标识符。不存在的图例会在响应中标记。单次最多支持100个图例。","example":["legend_001","legend_002","legend_003"],"items":{"type":"string","description":"要获取的图例ID列表，每个ID必须是有效的图例标识符。不存在的图例会在响应中标记。单次最多支持100个图例。","example":"[\"legend_001\",\"legend_002\",\"legend_003\"]"}}},"description":"批量获取请求，包含要获取的图例ID列表（最多100个）","example":{"batchRequests":["legend_001","legend_002","legend_003"]},"title":"LegendBatchGetRequest"}}},"required":true},"responses":{"200":{"description":"批量获取操作成功","content":{"application/json":{"schema":{"type":"object","title":"Operation","description":"操作类，表示异步操作的状态和结果，用于批量处理场景","required":["operationId","done"],"properties":{"operationId":{"type":"string","description":"操作标识符，用于唯一标识一个操作实例","example":"op_12345678","pattern":"^[a-zA-Z0-9_-]+$","maxLength":64},"metaData":{"type":"object","description":"操作的元数据信息，包含操作的附加信息","additionalProperties":true,"example":{"startTime":"2025-01-01T10:00:00Z","priority":"high"}},"done":{"type":"boolean","description":"操作是否已完成，true 表示已完成（成功或失败），false 表示仍在处理中","example":true},"result":{"type":"object","description":"操作成功时的结果数据，仅当 done=true 且无错误时有值","additionalProperties":true},"error":{"type":"object","title":"ApiError","description":"REST API 错误响应的主要数据结构，遵循 Google API 错误处理规范","required":["code","message","status","details"],"properties":{"code":{"type":"integer","description":"HTTP 状态码（数字），例如 403、404、500等","example":403,"minimum":100,"maximum":599},"message":{"type":"string","description":"面向开发者的错误消息，应该使用英文","example":"Permission denied","minLength":1,"maxLength":1000},"status":{"description":"RestAPI 对应的状态码枚举值","type":"string","title":"Code","enum":["OK","CANCELLED","UNKNOWN","INVALID_ARGUMENT","DEADLINE_EXCEEDED","NOT_FOUND","ALREADY_EXISTS","PERMISSION_DENIED","UNAUTHENTICATED","RESOURCE_EXHAUSTED","FAILED_PRECONDITION","ABORTED","OUT_OF_RANGE","UNIMPLEMENTED","INTERNAL","UNAVAILABLE","DATA_LOSS","PARTIAL_ELEMENT_UPDATE_FAILED"],"x-module":"error","x-category":"错误处理"},"details":{"description":"错误的详细信息","type":"object","title":"ErrorDetails","required":["reason"],"properties":{"reason":{"type":"string","description":"错误原因，标识错误的直接原因。格式为大写蛇形命名","example":"INVALID_REQUEST_FORMAT","pattern":"^[A-Z][A-Z0-9_]*[A-Z0-9]$","maxLength":63},"message":{"type":"string","description":"针对此错误发生的人类可读的解释说明","example":"请求参数格式不正确，缺少必需的字段 'name'","maxLength":500},"domain":{"type":"string","description":"错误域，表示错误原因所属的逻辑分组，通常是生成错误的服务名称","example":"deco.manycoreapis.com","maxLength":100},"metaData":{"type":"object","description":"关于此错误的附加结构化详细信息，键名限制为64个字符","additionalProperties":{"type":"string","maxLength":200},"example":{"maxAllowedSize":"1000","actualSize":"1500"}}},"additionalProperties":false,"x-module":"error","x-category":"错误处理"},"localizedMessage":{"description":"错误的本地化消息，可选字段","type":"object","title":"LocalizedMessage","required":["locale","message"],"properties":{"locale":{"type":"string","description":"消息所使用的语言环境","example":"zh-CN","pattern":"^[a-z]{2,3}(-[A-Z]{2})?(-[a-z0-9]{5,8})?(-[a-z0-9]{1,8})*$"},"message":{"type":"string","description":"本地化的消息内容","example":"权限不足，无法访问该资源","minLength":1,"maxLength":1000}},"additionalProperties":false,"x-module":"error","x-category":"错误处理"},"help":{"description":"错误的帮助信息，可选字段","type":"object","title":"Help","required":["desc","url"],"properties":{"desc":{"type":"string","description":"链接描述，说明该链接提供的帮助内容","example":"查看 API 使用指南","minLength":1,"maxLength":200},"url":{"type":"string","description":"帮助链接的 URL 地址","example":"https://docs.example.com/api-guide","format":"uri","maxLength":500}},"additionalProperties":false,"x-module":"error","x-category":"错误处理"}},"additionalProperties":false,"x-module":"error","x-category":"错误处理"}},"x-module":"operation","x-category":"操作管理"},"examples":{"批量获取响应示例":{"description":"批量获取响应示例","value":{"operationId":"op_batch_get_001","status":"FINISHED","data":{"elements":[{"id":"legend_001","productId":"ObQ1gR2nF8kE"}]}}}}}}},"400":{"description":"请求数据无效，如图例ID列表为空、ID格式错误等","content":{"application/json":{"schema":{"type":"object","title":"ApiError","description":"REST API 错误响应的主要数据结构，遵循 Google API 错误处理规范","required":["code","message","status","details"],"properties":{"code":{"type":"integer","description":"HTTP 状态码（数字），例如 403、404、500等","example":403,"minimum":100,"maximum":599},"message":{"type":"string","description":"面向开发者的错误消息，应该使用英文","example":"Permission denied","minLength":1,"maxLength":1000},"status":{"description":"RestAPI 对应的状态码枚举值","type":"string","title":"Code","enum":["OK","CANCELLED","UNKNOWN","INVALID_ARGUMENT","DEADLINE_EXCEEDED","NOT_FOUND","ALREADY_EXISTS","PERMISSION_DENIED","UNAUTHENTICATED","RESOURCE_EXHAUSTED","FAILED_PRECONDITION","ABORTED","OUT_OF_RANGE","UNIMPLEMENTED","INTERNAL","UNAVAILABLE","DATA_LOSS","PARTIAL_ELEMENT_UPDATE_FAILED"],"x-module":"error","x-category":"错误处理"},"details":{"description":"错误的详细信息","type":"object","title":"ErrorDetails","required":["reason"],"properties":{"reason":{"type":"string","description":"错误原因，标识错误的直接原因。格式为大写蛇形命名","example":"INVALID_REQUEST_FORMAT","pattern":"^[A-Z][A-Z0-9_]*[A-Z0-9]$","maxLength":63},"message":{"type":"string","description":"针对此错误发生的人类可读的解释说明","example":"请求参数格式不正确，缺少必需的字段 'name'","maxLength":500},"domain":{"type":"string","description":"错误域，表示错误原因所属的逻辑分组，通常是生成错误的服务名称","example":"deco.manycoreapis.com","maxLength":100},"metaData":{"type":"object","description":"关于此错误的附加结构化详细信息，键名限制为64个字符","additionalProperties":{"type":"string","maxLength":200},"example":{"maxAllowedSize":"1000","actualSize":"1500"}}},"additionalProperties":false,"x-module":"error","x-category":"错误处理"},"localizedMessage":{"description":"错误的本地化消息，可选字段","type":"object","title":"LocalizedMessage","required":["locale","message"],"properties":{"locale":{"type":"string","description":"消息所使用的语言环境","example":"zh-CN","pattern":"^[a-z]{2,3}(-[A-Z]{2})?(-[a-z0-9]{5,8})?(-[a-z0-9]{1,8})*$"},"message":{"type":"string","description":"本地化的消息内容","example":"权限不足，无法访问该资源","minLength":1,"maxLength":1000}},"additionalProperties":false,"x-module":"error","x-category":"错误处理"},"help":{"description":"错误的帮助信息，可选字段","type":"object","title":"Help","required":["desc","url"],"properties":{"desc":{"type":"string","description":"链接描述，说明该链接提供的帮助内容","example":"查看 API 使用指南","minLength":1,"maxLength":200},"url":{"type":"string","description":"帮助链接的 URL 地址","example":"https://docs.example.com/api-guide","format":"uri","maxLength":500}},"additionalProperties":false,"x-module":"error","x-category":"错误处理"}},"additionalProperties":false,"x-module":"error","x-category":"错误处理"}}}},"500":{"description":"服务器内部错误","content":{"application/json":{"schema":{"type":"object","title":"ApiError","description":"REST API 错误响应的主要数据结构，遵循 Google API 错误处理规范","required":["code","message","status","details"],"properties":{"code":{"type":"integer","description":"HTTP 状态码（数字），例如 403、404、500等","example":403,"minimum":100,"maximum":599},"message":{"type":"string","description":"面向开发者的错误消息，应该使用英文","example":"Permission denied","minLength":1,"maxLength":1000},"status":{"description":"RestAPI 对应的状态码枚举值","type":"string","title":"Code","enum":["OK","CANCELLED","UNKNOWN","INVALID_ARGUMENT","DEADLINE_EXCEEDED","NOT_FOUND","ALREADY_EXISTS","PERMISSION_DENIED","UNAUTHENTICATED","RESOURCE_EXHAUSTED","FAILED_PRECONDITION","ABORTED","OUT_OF_RANGE","UNIMPLEMENTED","INTERNAL","UNAVAILABLE","DATA_LOSS","PARTIAL_ELEMENT_UPDATE_FAILED"],"x-module":"error","x-category":"错误处理"},"details":{"description":"错误的详细信息","type":"object","title":"ErrorDetails","required":["reason"],"properties":{"reason":{"type":"string","description":"错误原因，标识错误的直接原因。格式为大写蛇形命名","example":"INVALID_REQUEST_FORMAT","pattern":"^[A-Z][A-Z0-9_]*[A-Z0-9]$","maxLength":63},"message":{"type":"string","description":"针对此错误发生的人类可读的解释说明","example":"请求参数格式不正确，缺少必需的字段 'name'","maxLength":500},"domain":{"type":"string","description":"错误域，表示错误原因所属的逻辑分组，通常是生成错误的服务名称","example":"deco.manycoreapis.com","maxLength":100},"metaData":{"type":"object","description":"关于此错误的附加结构化详细信息，键名限制为64个字符","additionalProperties":{"type":"string","maxLength":200},"example":{"maxAllowedSize":"1000","actualSize":"1500"}}},"additionalProperties":false,"x-module":"error","x-category":"错误处理"},"localizedMessage":{"description":"错误的本地化消息，可选字段","type":"object","title":"LocalizedMessage","required":["locale","message"],"properties":{"locale":{"type":"string","description":"消息所使用的语言环境","example":"zh-CN","pattern":"^[a-z]{2,3}(-[A-Z]{2})?(-[a-z0-9]{5,8})?(-[a-z0-9]{1,8})*$"},"message":{"type":"string","description":"本地化的消息内容","example":"权限不足，无法访问该资源","minLength":1,"maxLength":1000}},"additionalProperties":false,"x-module":"error","x-category":"错误处理"},"help":{"description":"错误的帮助信息，可选字段","type":"object","title":"Help","required":["desc","url"],"properties":{"desc":{"type":"string","description":"链接描述，说明该链接提供的帮助内容","example":"查看 API 使用指南","minLength":1,"maxLength":200},"url":{"type":"string","description":"帮助链接的 URL 地址","example":"https://docs.example.com/api-guide","format":"uri","maxLength":500}},"additionalProperties":false,"x-module":"error","x-category":"错误处理"}},"additionalProperties":false,"x-module":"error","x-category":"错误处理"}}}}},"method":"post","path":"/layout/api/v1/rest/designs/{obsDesignId}/levels/{levelId}/legend:batchget","servers":[{"url":"http://localhost:8080","description":"本地开发环境"},{"url":"https://api-dev.qunhe.com","description":"开发测试环境"},{"url":"https://api.qunhe.com","description":"生产环境"}],"jsonRequestBodyExample":{"batchRequests":["legend_001","legend_002","legend_003"]},"info":{"title":"户型图例管理API","description":"户型图例管理服务REST API\n\n提供图例数据的完整生命周期管理接口，图例是户型设计中用于标识空间元素的核心组件。支持单个和批量操作，包括图例的创建、查询、更新、删除以及复杂的图例组合功能。\n\n**主要功能特性：**\n- 单个图例CRUD操作：创建、获取、更新、删除单个图例\n- 批量操作支持：高效的批量创建、获取、更新、删除操作\n- 分页查询：支持大量图例数据的分页展示和管理\n- 图例组合：支持创建图例组合，实现复杂的空间布局\n- 异步处理：批量操作采用异步处理机制，确保大数据量操作的性能\n- 权限控制：严格的读写权限控制，保障数据安全\n\n**业务场景应用：**\n- 户型设计工具中的图例管理\n- 空间布局设计和优化\n- 图例模板和组合的快速应用\n- 户型数据的批量导入导出\n- 三维空间定位和图层管理\n\n**技术规范：**\n- 响应格式：JSON\n- 字符编码：UTF-8\n- 认证方式：基于设计方案的权限验证\n- 异步操作：支持长时间批量操作的异步处理","contact":{"name":"群核设计服务开发团队","url":"https://wiki.manycore.com/design-services","email":"<EMAIL>"},"license":{"name":"群核科技专有许可证","url":"https://manycore.com/license"},"version":"1.0.0"},"postman":{"name":"批量获取图例信息","description":{"content":"根据图例ID列表批量获取图例信息。支持异步处理，适用于大量数据查询、数据同步等场景。单次最多支持100个图例。","type":"text/plain"},"url":{"path":["layout","api","v1","rest","designs",":obsDesignId","levels",":levelId","legend:batchget"],"host":["{{baseUrl}}"],"query":[],"variable":[{"disabled":false,"description":{"content":"(Required) 设计方案的ID","type":"text/plain"},"type":"any","value":"","key":"obsDesignId"},{"disabled":false,"description":{"content":"(Required) 楼层ID","type":"text/plain"},"type":"any","value":"","key":"levelId"}]},"header":[{"key":"Content-Type","value":"application/json"},{"key":"Accept","value":"application/json"}],"method":"POST","body":{"mode":"raw","raw":"","options":{"raw":{"language":"json"}}}}}
sidebar_class_name: "post api-method"
info_path: docs/api/layout/户型图例管理api
custom_edit_url: null
---

import MethodEndpoint from "@theme/ApiExplorer/MethodEndpoint";
import ParamsDetails from "@theme/ParamsDetails";
import RequestSchema from "@theme/RequestSchema";
import StatusCodes from "@theme/StatusCodes";
import OperationTabs from "@theme/OperationTabs";
import TabItem from "@theme/TabItem";
import Heading from "@theme/Heading";

<Heading
  as={"h1"}
  className={"openapi__heading"}
  children={"批量获取图例信息"}
>
</Heading>

<MethodEndpoint
  method={"post"}
  path={"/layout/api/v1/rest/designs/{obsDesignId}/levels/{levelId}/legend:batchget"}
  context={"endpoint"}
>
  
</MethodEndpoint>



根据图例ID列表批量获取图例信息。支持异步处理，适用于大量数据查询、数据同步等场景。单次最多支持100个图例。

<Heading
  id={"request"}
  as={"h2"}
  className={"openapi-tabs__heading"}
  children={"Request"}
>
</Heading>

<ParamsDetails
  parameters={[{"name":"obsDesignId","in":"path","description":"设计方案的ID","required":true,"schema":{"type":"string"},"example":"ObQ1gR2nF8kE"},{"name":"levelId","in":"path","description":"楼层ID","required":true,"schema":{"type":"string"},"example":"level_001"}]}
>
  
</ParamsDetails>

<RequestSchema
  title={"Body"}
  body={{"content":{"application/json":{"schema":{"required":["batchRequests"],"type":"object","properties":{"requestId":{"type":"string"},"operationId":{"type":"string"},"batchRequests":{"type":"array","description":"要获取的图例ID列表，每个ID必须是有效的图例标识符。不存在的图例会在响应中标记。单次最多支持100个图例。","example":["legend_001","legend_002","legend_003"],"items":{"type":"string","description":"要获取的图例ID列表，每个ID必须是有效的图例标识符。不存在的图例会在响应中标记。单次最多支持100个图例。","example":"[\"legend_001\",\"legend_002\",\"legend_003\"]"}}},"description":"批量获取请求，包含要获取的图例ID列表（最多100个）","example":{"batchRequests":["legend_001","legend_002","legend_003"]},"title":"LegendBatchGetRequest"}}},"required":true}}
>
  
</RequestSchema>

<StatusCodes
  id={undefined}
  label={undefined}
  responses={{"200":{"description":"批量获取操作成功","content":{"application/json":{"schema":{"type":"object","title":"Operation","description":"操作类，表示异步操作的状态和结果，用于批量处理场景","required":["operationId","done"],"properties":{"operationId":{"type":"string","description":"操作标识符，用于唯一标识一个操作实例","example":"op_12345678","pattern":"^[a-zA-Z0-9_-]+$","maxLength":64},"metaData":{"type":"object","description":"操作的元数据信息，包含操作的附加信息","additionalProperties":true,"example":{"startTime":"2025-01-01T10:00:00Z","priority":"high"}},"done":{"type":"boolean","description":"操作是否已完成，true 表示已完成（成功或失败），false 表示仍在处理中","example":true},"result":{"type":"object","description":"操作成功时的结果数据，仅当 done=true 且无错误时有值","additionalProperties":true},"error":{"type":"object","title":"ApiError","description":"REST API 错误响应的主要数据结构，遵循 Google API 错误处理规范","required":["code","message","status","details"],"properties":{"code":{"type":"integer","description":"HTTP 状态码（数字），例如 403、404、500等","example":403,"minimum":100,"maximum":599},"message":{"type":"string","description":"面向开发者的错误消息，应该使用英文","example":"Permission denied","minLength":1,"maxLength":1000},"status":{"description":"RestAPI 对应的状态码枚举值","type":"string","title":"Code","enum":["OK","CANCELLED","UNKNOWN","INVALID_ARGUMENT","DEADLINE_EXCEEDED","NOT_FOUND","ALREADY_EXISTS","PERMISSION_DENIED","UNAUTHENTICATED","RESOURCE_EXHAUSTED","FAILED_PRECONDITION","ABORTED","OUT_OF_RANGE","UNIMPLEMENTED","INTERNAL","UNAVAILABLE","DATA_LOSS","PARTIAL_ELEMENT_UPDATE_FAILED"],"x-module":"error","x-category":"错误处理"},"details":{"description":"错误的详细信息","type":"object","title":"ErrorDetails","required":["reason"],"properties":{"reason":{"type":"string","description":"错误原因，标识错误的直接原因。格式为大写蛇形命名","example":"INVALID_REQUEST_FORMAT","pattern":"^[A-Z][A-Z0-9_]*[A-Z0-9]$","maxLength":63},"message":{"type":"string","description":"针对此错误发生的人类可读的解释说明","example":"请求参数格式不正确，缺少必需的字段 'name'","maxLength":500},"domain":{"type":"string","description":"错误域，表示错误原因所属的逻辑分组，通常是生成错误的服务名称","example":"deco.manycoreapis.com","maxLength":100},"metaData":{"type":"object","description":"关于此错误的附加结构化详细信息，键名限制为64个字符","additionalProperties":{"type":"string","maxLength":200},"example":{"maxAllowedSize":"1000","actualSize":"1500"}}},"additionalProperties":false,"x-module":"error","x-category":"错误处理"},"localizedMessage":{"description":"错误的本地化消息，可选字段","type":"object","title":"LocalizedMessage","required":["locale","message"],"properties":{"locale":{"type":"string","description":"消息所使用的语言环境","example":"zh-CN","pattern":"^[a-z]{2,3}(-[A-Z]{2})?(-[a-z0-9]{5,8})?(-[a-z0-9]{1,8})*$"},"message":{"type":"string","description":"本地化的消息内容","example":"权限不足，无法访问该资源","minLength":1,"maxLength":1000}},"additionalProperties":false,"x-module":"error","x-category":"错误处理"},"help":{"description":"错误的帮助信息，可选字段","type":"object","title":"Help","required":["desc","url"],"properties":{"desc":{"type":"string","description":"链接描述，说明该链接提供的帮助内容","example":"查看 API 使用指南","minLength":1,"maxLength":200},"url":{"type":"string","description":"帮助链接的 URL 地址","example":"https://docs.example.com/api-guide","format":"uri","maxLength":500}},"additionalProperties":false,"x-module":"error","x-category":"错误处理"}},"additionalProperties":false,"x-module":"error","x-category":"错误处理"}},"x-module":"operation","x-category":"操作管理"},"examples":{"批量获取响应示例":{"description":"批量获取响应示例","value":{"operationId":"op_batch_get_001","status":"FINISHED","data":{"elements":[{"id":"legend_001","productId":"ObQ1gR2nF8kE"}]}}}}}}},"400":{"description":"请求数据无效，如图例ID列表为空、ID格式错误等","content":{"application/json":{"schema":{"type":"object","title":"ApiError","description":"REST API 错误响应的主要数据结构，遵循 Google API 错误处理规范","required":["code","message","status","details"],"properties":{"code":{"type":"integer","description":"HTTP 状态码（数字），例如 403、404、500等","example":403,"minimum":100,"maximum":599},"message":{"type":"string","description":"面向开发者的错误消息，应该使用英文","example":"Permission denied","minLength":1,"maxLength":1000},"status":{"description":"RestAPI 对应的状态码枚举值","type":"string","title":"Code","enum":["OK","CANCELLED","UNKNOWN","INVALID_ARGUMENT","DEADLINE_EXCEEDED","NOT_FOUND","ALREADY_EXISTS","PERMISSION_DENIED","UNAUTHENTICATED","RESOURCE_EXHAUSTED","FAILED_PRECONDITION","ABORTED","OUT_OF_RANGE","UNIMPLEMENTED","INTERNAL","UNAVAILABLE","DATA_LOSS","PARTIAL_ELEMENT_UPDATE_FAILED"],"x-module":"error","x-category":"错误处理"},"details":{"description":"错误的详细信息","type":"object","title":"ErrorDetails","required":["reason"],"properties":{"reason":{"type":"string","description":"错误原因，标识错误的直接原因。格式为大写蛇形命名","example":"INVALID_REQUEST_FORMAT","pattern":"^[A-Z][A-Z0-9_]*[A-Z0-9]$","maxLength":63},"message":{"type":"string","description":"针对此错误发生的人类可读的解释说明","example":"请求参数格式不正确，缺少必需的字段 'name'","maxLength":500},"domain":{"type":"string","description":"错误域，表示错误原因所属的逻辑分组，通常是生成错误的服务名称","example":"deco.manycoreapis.com","maxLength":100},"metaData":{"type":"object","description":"关于此错误的附加结构化详细信息，键名限制为64个字符","additionalProperties":{"type":"string","maxLength":200},"example":{"maxAllowedSize":"1000","actualSize":"1500"}}},"additionalProperties":false,"x-module":"error","x-category":"错误处理"},"localizedMessage":{"description":"错误的本地化消息，可选字段","type":"object","title":"LocalizedMessage","required":["locale","message"],"properties":{"locale":{"type":"string","description":"消息所使用的语言环境","example":"zh-CN","pattern":"^[a-z]{2,3}(-[A-Z]{2})?(-[a-z0-9]{5,8})?(-[a-z0-9]{1,8})*$"},"message":{"type":"string","description":"本地化的消息内容","example":"权限不足，无法访问该资源","minLength":1,"maxLength":1000}},"additionalProperties":false,"x-module":"error","x-category":"错误处理"},"help":{"description":"错误的帮助信息，可选字段","type":"object","title":"Help","required":["desc","url"],"properties":{"desc":{"type":"string","description":"链接描述，说明该链接提供的帮助内容","example":"查看 API 使用指南","minLength":1,"maxLength":200},"url":{"type":"string","description":"帮助链接的 URL 地址","example":"https://docs.example.com/api-guide","format":"uri","maxLength":500}},"additionalProperties":false,"x-module":"error","x-category":"错误处理"}},"additionalProperties":false,"x-module":"error","x-category":"错误处理"}}}},"500":{"description":"服务器内部错误","content":{"application/json":{"schema":{"type":"object","title":"ApiError","description":"REST API 错误响应的主要数据结构，遵循 Google API 错误处理规范","required":["code","message","status","details"],"properties":{"code":{"type":"integer","description":"HTTP 状态码（数字），例如 403、404、500等","example":403,"minimum":100,"maximum":599},"message":{"type":"string","description":"面向开发者的错误消息，应该使用英文","example":"Permission denied","minLength":1,"maxLength":1000},"status":{"description":"RestAPI 对应的状态码枚举值","type":"string","title":"Code","enum":["OK","CANCELLED","UNKNOWN","INVALID_ARGUMENT","DEADLINE_EXCEEDED","NOT_FOUND","ALREADY_EXISTS","PERMISSION_DENIED","UNAUTHENTICATED","RESOURCE_EXHAUSTED","FAILED_PRECONDITION","ABORTED","OUT_OF_RANGE","UNIMPLEMENTED","INTERNAL","UNAVAILABLE","DATA_LOSS","PARTIAL_ELEMENT_UPDATE_FAILED"],"x-module":"error","x-category":"错误处理"},"details":{"description":"错误的详细信息","type":"object","title":"ErrorDetails","required":["reason"],"properties":{"reason":{"type":"string","description":"错误原因，标识错误的直接原因。格式为大写蛇形命名","example":"INVALID_REQUEST_FORMAT","pattern":"^[A-Z][A-Z0-9_]*[A-Z0-9]$","maxLength":63},"message":{"type":"string","description":"针对此错误发生的人类可读的解释说明","example":"请求参数格式不正确，缺少必需的字段 'name'","maxLength":500},"domain":{"type":"string","description":"错误域，表示错误原因所属的逻辑分组，通常是生成错误的服务名称","example":"deco.manycoreapis.com","maxLength":100},"metaData":{"type":"object","description":"关于此错误的附加结构化详细信息，键名限制为64个字符","additionalProperties":{"type":"string","maxLength":200},"example":{"maxAllowedSize":"1000","actualSize":"1500"}}},"additionalProperties":false,"x-module":"error","x-category":"错误处理"},"localizedMessage":{"description":"错误的本地化消息，可选字段","type":"object","title":"LocalizedMessage","required":["locale","message"],"properties":{"locale":{"type":"string","description":"消息所使用的语言环境","example":"zh-CN","pattern":"^[a-z]{2,3}(-[A-Z]{2})?(-[a-z0-9]{5,8})?(-[a-z0-9]{1,8})*$"},"message":{"type":"string","description":"本地化的消息内容","example":"权限不足，无法访问该资源","minLength":1,"maxLength":1000}},"additionalProperties":false,"x-module":"error","x-category":"错误处理"},"help":{"description":"错误的帮助信息，可选字段","type":"object","title":"Help","required":["desc","url"],"properties":{"desc":{"type":"string","description":"链接描述，说明该链接提供的帮助内容","example":"查看 API 使用指南","minLength":1,"maxLength":200},"url":{"type":"string","description":"帮助链接的 URL 地址","example":"https://docs.example.com/api-guide","format":"uri","maxLength":500}},"additionalProperties":false,"x-module":"error","x-category":"错误处理"}},"additionalProperties":false,"x-module":"error","x-category":"错误处理"}}}}}}
>
  
</StatusCodes>


      