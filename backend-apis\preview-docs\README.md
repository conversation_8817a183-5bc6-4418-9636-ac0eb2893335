# API 文档网站

这是一个基于 Docusaurus 和 [docusaurus-openapi-docs](https://github.com/PaloAltoNetworks/docusaurus-openapi-docs) 插件构建的简洁 API 文档网站。

## 特性

- ✨ 简洁清晰的界面设计
- 📚 自动根据 OpenAPI 规范生成文档
- 🔍 内置搜索功能
- 📱 响应式设计
- 🌓 深色/浅色主题切换

## 快速开始

### 安装依赖

```bash
npm install
```

### 生成 API 文档

```bash
npm run gen-api-docs all
```

### 启动开发服务器

```bash
npm start
```

### 构建生产版本

```bash
npm run build
```

## 配置说明

### OpenAPI 文件结构

OpenAPI 规范文件应放置在 `../openapi/` 目录下，结构如下：

```
openapi/
├── service1/
│   └── restapi.yaml
├── service2/
│   └── restapi.yaml
└── ...
```

### 主要配置文件

- `docusaurus.config.ts` - 主配置文件，已简化为专注于 API 文档展示
- `src/css/custom.css` - 简洁的样式配置
- `sidebars.ts` - 侧边栏配置（自动生成）

## 常用命令

```bash
# 清理生成的文档
npm run clean-api-docs all

# 重新生成所有文档
npm run gen-api-docs all

# 类型检查
npm run typecheck

# 清理构建缓存
npm run clear
```

## 部署

构建完成后，可将 `build/` 目录部署到任何静态网站托管服务。

## 自定义

如需自定义样式或主题，请修改：
- `src/css/custom.css` - 样式自定义
- `docusaurus.config.ts` - 配置自定义

## 注意事项

- 此配置专为内部 API 评审使用，去除了复杂的营销功能
- 专注于清晰的 API 文档展示
- 保持配置简洁，便于维护 