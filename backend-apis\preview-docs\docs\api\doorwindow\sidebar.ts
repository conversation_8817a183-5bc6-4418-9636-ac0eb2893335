import type { SidebarsConfig } from "@docusaurus/plugin-content-docs";

const sidebar: SidebarsConfig = {
  apisidebar: [
    {
      type: "doc",
      id: "api/doorwindow/门窗设计管理api",
    },
    {
      type: "category",
      label: "门窗管理 API",
      link: {
        type: "doc",
        id: "api/doorwindow/门窗管理-api",
      },
      items: [
        {
          type: "doc",
          id: "api/doorwindow/batch-update",
          label: "批量更新门窗",
          className: "api-method post",
        },
        {
          type: "doc",
          id: "api/doorwindow/get-doc",
          label: "获取门窗文档",
          className: "api-method get",
        },
      ],
    },
  ],
};

export default sidebar.apisidebar;


