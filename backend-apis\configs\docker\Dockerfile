FROM maven:3-jdk-11

# Set environment variables
ENV DEBIAN_FRONTEND=noninteractive
ENV OPENAPI_GENERATOR_VERSION=7.12.0
ENV OPENAPI_JAR_PATH=/opt/openapi-generator/openapi-generator-cli-${OPENAPI_GENERATOR_VERSION}.jar
ENV NODE_VERSION=18

# Update package lists using <PERSON>yun mirror, install curl, git, and Node.js
RUN sed -i 's/deb.debian.org/mirrors.aliyun.com/g' /etc/apt/sources.list && \
    sed -i 's/security.debian.org/mirrors.aliyun.com/g' /etc/apt/sources.list && \
    apt-get update && \
    apt-get install -y curl git expect && \
    # Install Node.js 18.x from NodeSource
    curl -fsSL https://deb.nodesource.com/setup_18.x | bash - && \
    apt-get install -y nodejs && \
    # Clean up apt cache
    rm -rf /var/lib/apt/lists/*

# Configure npm to use Aliyun registry by default for faster downloads
RUN npm config set registry https://registry.npmmirror.com

# Configure npm to use Qunhe private registry for @qunhe packages
RUN npm config set @qunhe:registry http://npm-registry.qunhequnhe.com

# Download the specific openapi-generator-cli JAR from Aliyun Maven mirror
RUN mkdir -p /opt/openapi-generator && \
    echo "Downloading openapi-generator-cli-${OPENAPI_GENERATOR_VERSION}.jar from Aliyun mirror..." && \
    curl -fSL "https://maven.aliyun.com/repository/central/org/openapitools/openapi-generator-cli/${OPENAPI_GENERATOR_VERSION}/openapi-generator-cli-${OPENAPI_GENERATOR_VERSION}.jar" -o ${OPENAPI_JAR_PATH} && \
    echo "Download complete."

# Pre-install commonly used npm packages for CI
RUN npm install -g @stoplight/spectral-cli redoc-cli

# Install Qunhe manual tools
RUN echo "Installing Qunhe manual tools..." && \
    # Install @qunhe/def-next-cli using npm
    npm install -g @qunhe/def-next-cli && \
    # Verify kjl installation
    kjl --version && \
    echo "kjl tool installed successfully"

# Install manual tasks (requires kjl to be available)
RUN echo "Installing manual tasks..." && \
    # Create a temporary directory for manual task installation
    mkdir -p /tmp/manual-setup && \
    cd /tmp/manual-setup && \
    # Initialize a basic package.json for manual task installation
    echo '{"name": "manual-setup", "version": "1.0.0"}' > package.json && \
    # Install manual tasks
    kjl task install manual && \
    echo "Manual tasks installed successfully" && \
    # Clean up temporary directory
    cd / && rm -rf /tmp/manual-setup

# Pre-install docs-site dependencies to speed up CI
# This optimization can reduce CI dependency installation time from ~2-3 minutes to ~10 seconds
COPY docs-site/package*.json /tmp/docs-site/
RUN echo "Pre-installing docs-site dependencies..." && \
    cd /tmp/docs-site && \
    # Use aliyun npm registry for faster downloads in China
    npm config set registry https://registry.npmmirror.com && \
    # Check if package-lock.json exists and install accordingly
    if [ -f "package-lock.json" ]; then \
        echo "Found package-lock.json, using npm ci..." && \
        npm ci --only=production && \
        npm ci --only=development; \
    else \
        echo "No package-lock.json found, using npm install..." && \
        npm install --only=production && \
        npm install --only=development; \
    fi && \
    # Cache node_modules in a known location
    mkdir -p /opt/docs-dependencies && \
    cp -r node_modules /opt/docs-dependencies/ && \
    # Clean up temporary files
    cd / && rm -rf /tmp/docs-site && \
    echo "docs-site dependencies cached successfully"

# Create a script to quickly restore cached dependencies
RUN echo '#!/bin/bash' > /usr/local/bin/restore-docs-deps && \
    echo 'set -e' >> /usr/local/bin/restore-docs-deps && \
    echo 'if [ -d "/opt/docs-dependencies/node_modules" ]; then' >> /usr/local/bin/restore-docs-deps && \
    echo '  echo "🚀 恢复缓存的 docs-site 依赖..."' >> /usr/local/bin/restore-docs-deps && \
    echo '  if [ ! -d "node_modules" ]; then' >> /usr/local/bin/restore-docs-deps && \
    echo '    cp -r /opt/docs-dependencies/node_modules ./' >> /usr/local/bin/restore-docs-deps && \
    echo '    echo "✅ 依赖恢复完成"' >> /usr/local/bin/restore-docs-deps && \
    echo '    exit 0' >> /usr/local/bin/restore-docs-deps && \
    echo '  else' >> /usr/local/bin/restore-docs-deps && \
    echo '    echo "ℹ️ node_modules 已存在，跳过恢复"' >> /usr/local/bin/restore-docs-deps && \
    echo '    exit 0' >> /usr/local/bin/restore-docs-deps && \
    echo '  fi' >> /usr/local/bin/restore-docs-deps && \
    echo 'else' >> /usr/local/bin/restore-docs-deps && \
    echo '  echo "⚠️ 缓存未找到，需要手动安装依赖"' >> /usr/local/bin/restore-docs-deps && \
    echo '  exit 1' >> /usr/local/bin/restore-docs-deps && \
    echo 'fi' >> /usr/local/bin/restore-docs-deps && \
    chmod +x /usr/local/bin/restore-docs-deps

# Set a working directory (optional)
WORKDIR /app

# Verify installations (optional)
RUN java -version
RUN mvn -version
RUN node -v
RUN npm -v
RUN git --version
RUN expect -version
RUN spectral --version
RUN redoc-cli --version
# Verify JAR download by attempting to run version command
RUN java -jar ${OPENAPI_JAR_PATH} version
# Verify kjl and manual tools
RUN kjl --version
RUN echo "Verifying kjl path..." && which kjl
RUN echo "Verifying manual tasks..." && (kjl task list | grep -i manual || echo "Manual tasks verification completed")
RUN echo "Verifying cached dependencies..." && ls -la /opt/docs-dependencies/ && echo "Dependencies cache verified"
RUN echo "Verifying restore script..." && which restore-docs-deps && echo "Restore script verified"
RUN echo "All tools verified successfully!"

# Default command (optional, can be overridden in .gitlab-ci.yml)
# CMD ["bash"] 