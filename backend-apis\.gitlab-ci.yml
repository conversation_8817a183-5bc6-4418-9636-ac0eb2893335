# .gitlab-ci.yml (at the root of backend-api-sdk)

# 默认配置：重试机制和超时设置
default:
  retry:
    max: 2
    when:
      - runner_system_failure
      - stuck_or_timeout_failure
      - api_failure
      - scheduler_failure
  timeout: 30m
  interruptible: true
  # 全局 before_script - 镜像拉取优化
  before_script:
    # 配置 Docker 镜像加速
    - |
      echo "🔧 配置 Docker 镜像加速..."
      mkdir -p /etc/docker
      cat > /etc/docker/daemon.json <<EOF
      {
        "registry-mirrors": [
          "https://docker.mirrors.ustc.edu.cn",
          "https://hub-mirror.c.163.com",
          "https://registry.docker-cn.com"
        ],
        "max-concurrent-downloads": 3,
        "max-concurrent-uploads": 3,
        "log-level": "warn"
      }
      EOF
      # 重启 Docker 服务（如果可能）
      if command -v systemctl >/dev/null 2>&1; then
        systemctl reload docker || true
      fi
    - chmod +x tools/ci-cd/ci/common/docker-image-pull.sh
    - source tools/ci-cd/ci/common/docker-image-pull.sh

# Use your custom pre-built image containing Java, Maven, Node, npm, and openapi-generator-cli
# IMPORTANT: Replace this with the actual path to your image in your container registry
# 如果私有 registry 不可用，考虑使用备用镜像
image: registry-qunhe.qunhequnhe.com/display/openapi-generator:latest

# Docker 服务配置 - 优化镜像拉取和网络超时
services:
  - name: docker:dind
    command:
      - "--registry-mirror=https://registry-qunhe.qunhequnhe.com"
      - "--max-concurrent-downloads=3"
      - "--max-concurrent-uploads=3"
      - "--debug"

variables:
  MAVEN_OPTS: "-Dmaven.repo.local=$CI_PROJECT_DIR/.m2/repository"
  # Docker registry settings
  DOCKER_REGISTRY: "registry-qunhe.qunhequnhe.com"
  DOCKER_IMAGE: "registry-qunhe.qunhequnhe.com/display/openapi-generator:latest"
  DOCKER_DRIVER: overlay2
  # 备用镜像（如果私有 registry 不可用）
  FALLBACK_IMAGE: "registry-qunhe02.qunhequnhe.com/display/openapi-generator:latest"
  # Node.js tools path
  NODE_PATH: /usr/local/lib/node_modules
  # Docker 拉取配置优化
  DOCKER_PULL_TIMEOUT: "600"  # 10分钟超时
  DOCKER_PULL_POLICY: "if-not-present"
  # 镜像拉取重试配置
  IMAGE_PULL_RETRIES: "3"
  IMAGE_PULL_DELAY: "30"
  # 调试模式（设置为 true 可以看到更详细的日志）
  DOCKER_DEBUG: "false"
  # Docker Hub 镜像加速配置
  DOCKER_HUB_MIRROR: "https://docker.mirrors.ustc.edu.cn"
  # 网络超时配置
  DOCKER_CLIENT_TIMEOUT: "120"
  COMPOSE_HTTP_TIMEOUT: "120"

  # 所有服务的配置
  CAMERA_RESTAPI: backend-apis/specifications/services/camera/openapi.yaml
  CAMERA_CONFIG_JAVA: backend-apis/generators/sdks/configs/java/camera.yaml
  CAMERA_OUTPUT_JAVA: build/sdks/camera/java
  DOORWINDOW_RESTAPI: backend-apis/specifications/services/doorwindow/openapi.yaml
  DOORWINDOW_CONFIG_JAVA: backend-apis/generators/sdks/configs/java/doorwindow.yaml
  DOORWINDOW_OUTPUT_JAVA: build/sdks/doorwindow/java
  FURNITURE_RESTAPI: backend-apis/specifications/services/furniture/openapi.yaml
  FURNITURE_CONFIG_JAVA: backend-apis/generators/sdks/configs/java/furniture.yaml
  FURNITURE_OUTPUT_JAVA: build/sdks/furniture/java
  KOOLUX_RESTAPI: backend-apis/specifications/services/koolux/openapi.yaml
  KOOLUX_CONFIG_JAVA: backend-apis/generators/sdks/configs/java/koolux.yaml
  KOOLUX_OUTPUT_JAVA: build/sdks/koolux/java
  LAYOUT_RESTAPI: backend-apis/specifications/services/layout/openapi.yaml
  LAYOUT_CONFIG_JAVA: backend-apis/generators/sdks/configs/java/layout.yaml
  LAYOUT_OUTPUT_JAVA: build/sdks/layout/java
  PDM_RESTAPI: backend-apis/specifications/services/pdm/openapi.yaml
  PDM_CONFIG_JAVA: backend-apis/generators/sdks/configs/java/pdm.yaml
  PDM_OUTPUT_JAVA: build/sdks/pdm/java

cache:
  key: "$CI_COMMIT_REF_SLUG"
  paths:
    - .m2/repository/
    - target/
    - node_modules/
    - /usr/local/lib/node_modules
    - backend-apis/preview-docs/node_modules/
    - backend-apis/preview-docs/.docusaurus/
    - backend-apis/preview-docs/build/
    # Webpack缓存目录
    - backend-apis/preview-docs/.docusaurus/webpack-cache/

stages:
  - health_check  # 新增健康检查阶段
  - mr_pipeline
  # 主干分支流程 stages
  - generate_sdk
  - build
  - test
  - deploy

# --- 健康检查 Jobs ---

# Docker 镜像仓库健康检查
registry_health_check:
  stage: health_check
  tags:
    - kube-runner
  timeout: 5m
  retry:
    max: 1
    when:
      - runner_system_failure
      - stuck_or_timeout_failure
  script:
    - chmod +x tools/ci-cd/ci/common/registry-health-check.sh
    - ./tools/ci-cd/ci/common/registry-health-check.sh
  allow_failure: true  # 允许失败，不阻塞后续流程
  rules:
    - if: '$CI_MERGE_REQUEST_IID'
    - if: '$CI_COMMIT_BRANCH == "master" || $CI_COMMIT_BRANCH == "develop"'
    - if: '$CI_PIPELINE_SOURCE == "web"'

# --- MR 流程 Jobs (合并版本) ---

# 完整 MR 流程 - 可选的详细检查（允许失败）
mr_complete_pipeline:
  stage: mr_pipeline
  tags:
    - kube-runner
  timeout: 30m
  retry:
    max: 2  # GitLab CI 最大重试次数
    when:
      - runner_system_failure
      - stuck_or_timeout_failure
      - api_failure
      - scheduler_failure
      - job_execution_timeout  # 添加超时重试
  variables:
    MANUAL_ENV: "dev"
    # 针对此 job 的镜像拉取配置
    IMAGE_PULL_RETRIES: "5"
    IMAGE_PULL_DELAY: "60"
    DOCKER_PULL_TIMEOUT: "900"  # 15分钟
  script:
    # 执行完整的 MR 流程
    - chmod +x tools/ci-cd/ci/mr/complete-mr-pipeline.sh
    - ./tools/ci-cd/ci/mr/complete-mr-pipeline.sh
  artifacts:
    paths:
      - backend-apis/preview-docs/build/
      - deploy_info.env    # 只保留部署信息，GitLab environment 需要
    reports:
      dotenv:
        - deploy_info.env  # 只保留部署信息
    expire_in: 1 week
    when: always
  environment:
    name: manual-dev-mr-$CI_MERGE_REQUEST_IID
    url: $DEPLOY_URL
    deployment_tier: development
    auto_stop_in: 1 week
  allow_failure: false
  rules:
    - if: '$CI_MERGE_REQUEST_IID'
    - if: '$CI_COMMIT_BRANCH == "master" || $CI_COMMIT_BRANCH == "develop"'
    - if: '$CI_PIPELINE_SOURCE == "web"'


# 清理预览环境
cleanup_preview:
  stage: mr_pipeline
  tags:
    - kube-runner
  script:
    - echo "🧹 清理 MR 预览环境..."
  environment:
    name: manual-dev-mr-$CI_MERGE_REQUEST_IID
    action: stop
  when: manual
  allow_failure: true
  rules:
    - if: '$CI_MERGE_REQUEST_IID'

# --- 主干分支流程 Jobs ---

# SDK 生成
generate_sdks:
  stage: generate_sdk
  tags:
    - kube-runner
  script:
    - chmod +x tools/ci-cd/ci/deployment/generate-sdks.sh
    - ./tools/ci-cd/ci/deployment/generate-sdks.sh
  artifacts:
    paths:
      - build/
    expire_in: 1 day
  rules:
    - if: '$CI_COMMIT_BRANCH == "master" || $CI_COMMIT_BRANCH == "develop"'
    - if: '$CI_COMMIT_TAG'

# 构建 SDK
build_sdks:
  stage: build
  tags:
    - kube-runner
  script:
    - chmod +x tools/ci-cd/ci/deployment/build-sdks.sh
    - ./tools/ci-cd/ci/deployment/build-sdks.sh
  needs: [generate_sdks]
  rules:
    - if: '$CI_COMMIT_BRANCH == "master" || $CI_COMMIT_BRANCH == "develop"'
    - if: '$CI_COMMIT_TAG'

# 测试 SDK
test_sdks:
  stage: test
  tags:
    - kube-runner
  script:
    - chmod +x tools/ci-cd/ci/deployment/test-sdks.sh
    - ./tools/ci-cd/ci/deployment/test-sdks.sh
  needs: [build_sdks]
  rules:
    - if: '$CI_COMMIT_BRANCH == "master" || $CI_COMMIT_BRANCH == "develop"'
    - if: '$CI_COMMIT_TAG'

# 部署快照版本
deploy_snapshots:
  stage: deploy
  tags:
    - kube-runner
  script:
    - chmod +x tools/ci-cd/ci/deployment/deploy-snapshots.sh
    - ./tools/ci-cd/ci/deployment/deploy-snapshots.sh
  needs: [test_sdks]
  rules:
    - if: '$CI_COMMIT_BRANCH == "master" || $CI_COMMIT_BRANCH == "develop"'

# 部署正式版本
deploy_releases:
  stage: deploy
  tags:
    - kube-runner
  script:
    - chmod +x tools/ci-cd/ci/deployment/deploy-releases.sh
    - ./tools/ci-cd/ci/deployment/deploy-releases.sh
  needs: [test_sdks]
  when: manual
  rules:
    - if: '$CI_COMMIT_TAG'

# === Docusaurus 文档部署 ===

# 部署到群核科技 Manual 网站 (开发环境)
deploy_manual_dev:
  stage: deploy
  tags:
    - kube-runner
  timeout: 20m
  retry:
    max: 2
    when:
      - runner_system_failure
      - stuck_or_timeout_failure
      - api_failure
      - scheduler_failure
      - script_failure
  variables:
    MANUAL_ENV: "dev"
  before_script:
    - node --version
    - npm --version
    # 确保docs-site目录存在package-lock.json
    - |
      if [ ! -f "backend-apis/preview-docs/package-lock.json" ]; then
        echo "⚠️ package-lock.json 不存在，生成新的..."
        cd backend-apis/preview-docs
        npm install --package-lock-only
        cd ../..
      fi
  script:
    - chmod +x tools/ci-cd/ci/docs/deploy-to-manual.sh
    - ./tools/ci-cd/ci/docs/deploy-to-manual.sh
  artifacts:
    paths:
      - public/
      - deploy_info.env
    reports:
      dotenv: deploy_info.env
    expire_in: 7 days
    when: always
  environment:
    name: manual-dev
    url: $DEPLOY_URL
    deployment_tier: development
  rules:
    - if: '$CI_COMMIT_BRANCH != "master" && $CI_COMMIT_BRANCH != "main" && $CI_COMMIT_TAG == null && $CI_MERGE_REQUEST_IID == null'
      when: manual

# 部署到群核科技 Manual 网站 (预发布环境)
deploy_manual_staging:
  stage: deploy
  tags:
    - kube-runner
  timeout: 20m
  retry:
    max: 2
    when:
      - runner_system_failure
      - stuck_or_timeout_failure
      - api_failure
      - scheduler_failure
      - script_failure
  variables:
    MANUAL_ENV: "staging"
  before_script:
    - node --version
    - npm --version
    # 确保docs-site目录存在package-lock.json
    - |
      if [ ! -f "backend-apis/preview-docs/package-lock.json" ]; then
        echo "⚠️ package-lock.json 不存在，生成新的..."
        cd backend-apis/preview-docs
        npm install --package-lock-only
        cd ../..
      fi
  script:
    - chmod +x tools/ci-cd/ci/docs/deploy-to-manual.sh
    - ./tools/ci-cd/ci/docs/deploy-to-manual.sh
  artifacts:
    paths:
      - public/
      - deploy_info.env
    reports:
      dotenv: deploy_info.env
    expire_in: 30 days
    when: always
  environment:
    name: manual-staging
    url: $DEPLOY_URL
    deployment_tier: staging
  rules:
    - if: '$CI_COMMIT_BRANCH == "master" || $CI_COMMIT_BRANCH == "main"'
      when: manual

# 部署到群核科技 Manual 网站 (生产环境)
deploy_manual_prod:
  stage: deploy
  tags:
    - kube-runner
  timeout: 25m
  retry:
    max: 2
    when:
      - runner_system_failure
      - stuck_or_timeout_failure
      - api_failure
      - scheduler_failure
      - script_failure
  variables:
    MANUAL_ENV: "prod"
  before_script:
    - node --version
    - npm --version
    # 确保docs-site目录存在package-lock.json
    - |
      if [ ! -f "backend-apis/preview-docs/package-lock.json" ]; then
        echo "⚠️ package-lock.json 不存在，生成新的..."
        cd backend-apis/preview-docs
        npm install --package-lock-only
        cd ../..
      fi
  script:
    - chmod +x tools/ci-cd/ci/docs/deploy-to-manual.sh
    - ./tools/ci-cd/ci/docs/deploy-to-manual.sh
  artifacts:
    paths:
      - public/
      - deploy_info.env
    reports:
      dotenv: deploy_info.env
    expire_in: 1 year
    when: always
  environment:
    name: manual-production
    url: $DEPLOY_URL
    deployment_tier: production
  rules:
    - if: '$CI_COMMIT_TAG'
      when: manual




