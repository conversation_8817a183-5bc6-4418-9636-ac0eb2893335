{"id": "api/furniture/家具设计管理api", "title": "家具设计管理API", "description": "家具设计服务REST API", "source": "@site/docs/api/furniture/家具设计管理api.info.mdx", "sourceDirName": "api/furniture", "slug": "/api/furniture/家具设计管理api", "permalink": "/manycoreapi-demo/0.0.4/docs/api/furniture/家具设计管理api", "draft": false, "unlisted": false, "editUrl": null, "tags": [], "version": "current", "sidebarPosition": 0, "frontMatter": {"id": "家具设计管理api", "title": "家具设计管理API", "description": "家具设计服务REST API", "sidebar_label": "Introduction", "sidebar_position": 0, "hide_title": true, "custom_edit_url": null}, "sidebar": "furnitureSidebar", "next": {"title": "家具管理接口", "permalink": "/manycoreapi-demo/0.0.4/docs/api/furniture/家具管理接口"}}