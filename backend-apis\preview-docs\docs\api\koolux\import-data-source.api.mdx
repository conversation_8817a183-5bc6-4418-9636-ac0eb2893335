---
id: import-data-source
title: "创建KooLux方案"
description: "导入酷家乐方案到KooLux"
sidebar_label: "创建KooLux方案"
hide_title: true
hide_table_of_contents: true
api: {"tags":["KooLuxLight"],"description":"导入酷家乐方案到KooLux","operationId":"importDataSource","requestBody":{"description":"create koolux design request data","content":{"application/json":{"schema":{"required":["bizTypeId","dataSourceId","type"],"type":"object","properties":{"requestId":{"type":"string","description":"请求ID，用于幂等性控制"},"operationId":{"type":"string","description":"操作ID，用于异步操作跟踪"},"dataSourceId":{"type":"string","description":"数据源ID（如酷家乐方案，则是酷家乐的designId）"},"type":{"type":"string","description":"数据源类型，UNSPECIFIED(未定义)，KJL(酷家乐)","enum":["UNSPECIFIED","KJL"]},"bizTypeId":{"type":"integer","description":"业务线ID，联系KooLux分配","format":"int32"}},"title":"CreateDesignRequest"}}},"required":true},"responses":{"200":{"description":"Success","content":{"application/json":{"schema":{"type":"object","properties":{"dataSourceId":{"type":"string","description":"数据源ID（如酷家乐方案，则是酷家乐的designId）"},"type":{"type":"string","description":"数据源类型，UNSPECIFIED(未定义)，KJL(酷家乐)","enum":["UNSPECIFIED","KJL"]},"kooLuxDesignId":{"type":"string","description":"KooLux方案ID"}},"title":"CreateDesignResponse"}}}},"400":{"description":"Invalid Argument","content":{"*/*":{"schema":{"type":"object","title":"ApiError","description":"REST API 错误响应的主要数据结构，遵循 Google API 错误处理规范","required":["code","message","status","details"],"properties":{"code":{"type":"integer","description":"HTTP 状态码（数字），例如 403、404、500等","example":403,"minimum":100,"maximum":599},"message":{"type":"string","description":"面向开发者的错误消息，应该使用英文","example":"Permission denied","minLength":1,"maxLength":1000},"status":{"description":"RestAPI 对应的状态码枚举值","type":"string","title":"Code","enum":["OK","CANCELLED","UNKNOWN","INVALID_ARGUMENT","DEADLINE_EXCEEDED","NOT_FOUND","ALREADY_EXISTS","PERMISSION_DENIED","UNAUTHENTICATED","RESOURCE_EXHAUSTED","FAILED_PRECONDITION","ABORTED","OUT_OF_RANGE","UNIMPLEMENTED","INTERNAL","UNAVAILABLE","DATA_LOSS","PARTIAL_ELEMENT_UPDATE_FAILED"],"x-module":"error","x-category":"错误处理"},"details":{"description":"错误的详细信息","type":"object","title":"ErrorDetails","required":["reason"],"properties":{"reason":{"type":"string","description":"错误原因，标识错误的直接原因。格式为大写蛇形命名","example":"INVALID_REQUEST_FORMAT","pattern":"^[A-Z][A-Z0-9_]*[A-Z0-9]$","maxLength":63},"message":{"type":"string","description":"针对此错误发生的人类可读的解释说明","example":"请求参数格式不正确，缺少必需的字段 'name'","maxLength":500},"domain":{"type":"string","description":"错误域，表示错误原因所属的逻辑分组，通常是生成错误的服务名称","example":"deco.manycoreapis.com","maxLength":100},"metaData":{"type":"object","description":"关于此错误的附加结构化详细信息，键名限制为64个字符","additionalProperties":{"type":"string","maxLength":200},"example":{"maxAllowedSize":"1000","actualSize":"1500"}}},"additionalProperties":false,"x-module":"error","x-category":"错误处理"},"localizedMessage":{"description":"错误的本地化消息，可选字段","type":"object","title":"LocalizedMessage","required":["locale","message"],"properties":{"locale":{"type":"string","description":"消息所使用的语言环境","example":"zh-CN","pattern":"^[a-z]{2,3}(-[A-Z]{2})?(-[a-z0-9]{5,8})?(-[a-z0-9]{1,8})*$"},"message":{"type":"string","description":"本地化的消息内容","example":"权限不足，无法访问该资源","minLength":1,"maxLength":1000}},"additionalProperties":false,"x-module":"error","x-category":"错误处理"},"help":{"description":"错误的帮助信息，可选字段","type":"object","title":"Help","required":["desc","url"],"properties":{"desc":{"type":"string","description":"链接描述，说明该链接提供的帮助内容","example":"查看 API 使用指南","minLength":1,"maxLength":200},"url":{"type":"string","description":"帮助链接的 URL 地址","example":"https://docs.example.com/api-guide","format":"uri","maxLength":500}},"additionalProperties":false,"x-module":"error","x-category":"错误处理"}},"additionalProperties":false,"x-module":"error","x-category":"错误处理"}}}},"401":{"description":"Unauthenticated","content":{"*/*":{"schema":{"type":"object","title":"ApiError","description":"REST API 错误响应的主要数据结构，遵循 Google API 错误处理规范","required":["code","message","status","details"],"properties":{"code":{"type":"integer","description":"HTTP 状态码（数字），例如 403、404、500等","example":403,"minimum":100,"maximum":599},"message":{"type":"string","description":"面向开发者的错误消息，应该使用英文","example":"Permission denied","minLength":1,"maxLength":1000},"status":{"description":"RestAPI 对应的状态码枚举值","type":"string","title":"Code","enum":["OK","CANCELLED","UNKNOWN","INVALID_ARGUMENT","DEADLINE_EXCEEDED","NOT_FOUND","ALREADY_EXISTS","PERMISSION_DENIED","UNAUTHENTICATED","RESOURCE_EXHAUSTED","FAILED_PRECONDITION","ABORTED","OUT_OF_RANGE","UNIMPLEMENTED","INTERNAL","UNAVAILABLE","DATA_LOSS","PARTIAL_ELEMENT_UPDATE_FAILED"],"x-module":"error","x-category":"错误处理"},"details":{"description":"错误的详细信息","type":"object","title":"ErrorDetails","required":["reason"],"properties":{"reason":{"type":"string","description":"错误原因，标识错误的直接原因。格式为大写蛇形命名","example":"INVALID_REQUEST_FORMAT","pattern":"^[A-Z][A-Z0-9_]*[A-Z0-9]$","maxLength":63},"message":{"type":"string","description":"针对此错误发生的人类可读的解释说明","example":"请求参数格式不正确，缺少必需的字段 'name'","maxLength":500},"domain":{"type":"string","description":"错误域，表示错误原因所属的逻辑分组，通常是生成错误的服务名称","example":"deco.manycoreapis.com","maxLength":100},"metaData":{"type":"object","description":"关于此错误的附加结构化详细信息，键名限制为64个字符","additionalProperties":{"type":"string","maxLength":200},"example":{"maxAllowedSize":"1000","actualSize":"1500"}}},"additionalProperties":false,"x-module":"error","x-category":"错误处理"},"localizedMessage":{"description":"错误的本地化消息，可选字段","type":"object","title":"LocalizedMessage","required":["locale","message"],"properties":{"locale":{"type":"string","description":"消息所使用的语言环境","example":"zh-CN","pattern":"^[a-z]{2,3}(-[A-Z]{2})?(-[a-z0-9]{5,8})?(-[a-z0-9]{1,8})*$"},"message":{"type":"string","description":"本地化的消息内容","example":"权限不足，无法访问该资源","minLength":1,"maxLength":1000}},"additionalProperties":false,"x-module":"error","x-category":"错误处理"},"help":{"description":"错误的帮助信息，可选字段","type":"object","title":"Help","required":["desc","url"],"properties":{"desc":{"type":"string","description":"链接描述，说明该链接提供的帮助内容","example":"查看 API 使用指南","minLength":1,"maxLength":200},"url":{"type":"string","description":"帮助链接的 URL 地址","example":"https://docs.example.com/api-guide","format":"uri","maxLength":500}},"additionalProperties":false,"x-module":"error","x-category":"错误处理"}},"additionalProperties":false,"x-module":"error","x-category":"错误处理"}}}},"404":{"description":"Design not found","content":{"*/*":{"schema":{"type":"object","title":"ApiError","description":"REST API 错误响应的主要数据结构，遵循 Google API 错误处理规范","required":["code","message","status","details"],"properties":{"code":{"type":"integer","description":"HTTP 状态码（数字），例如 403、404、500等","example":403,"minimum":100,"maximum":599},"message":{"type":"string","description":"面向开发者的错误消息，应该使用英文","example":"Permission denied","minLength":1,"maxLength":1000},"status":{"description":"RestAPI 对应的状态码枚举值","type":"string","title":"Code","enum":["OK","CANCELLED","UNKNOWN","INVALID_ARGUMENT","DEADLINE_EXCEEDED","NOT_FOUND","ALREADY_EXISTS","PERMISSION_DENIED","UNAUTHENTICATED","RESOURCE_EXHAUSTED","FAILED_PRECONDITION","ABORTED","OUT_OF_RANGE","UNIMPLEMENTED","INTERNAL","UNAVAILABLE","DATA_LOSS","PARTIAL_ELEMENT_UPDATE_FAILED"],"x-module":"error","x-category":"错误处理"},"details":{"description":"错误的详细信息","type":"object","title":"ErrorDetails","required":["reason"],"properties":{"reason":{"type":"string","description":"错误原因，标识错误的直接原因。格式为大写蛇形命名","example":"INVALID_REQUEST_FORMAT","pattern":"^[A-Z][A-Z0-9_]*[A-Z0-9]$","maxLength":63},"message":{"type":"string","description":"针对此错误发生的人类可读的解释说明","example":"请求参数格式不正确，缺少必需的字段 'name'","maxLength":500},"domain":{"type":"string","description":"错误域，表示错误原因所属的逻辑分组，通常是生成错误的服务名称","example":"deco.manycoreapis.com","maxLength":100},"metaData":{"type":"object","description":"关于此错误的附加结构化详细信息，键名限制为64个字符","additionalProperties":{"type":"string","maxLength":200},"example":{"maxAllowedSize":"1000","actualSize":"1500"}}},"additionalProperties":false,"x-module":"error","x-category":"错误处理"},"localizedMessage":{"description":"错误的本地化消息，可选字段","type":"object","title":"LocalizedMessage","required":["locale","message"],"properties":{"locale":{"type":"string","description":"消息所使用的语言环境","example":"zh-CN","pattern":"^[a-z]{2,3}(-[A-Z]{2})?(-[a-z0-9]{5,8})?(-[a-z0-9]{1,8})*$"},"message":{"type":"string","description":"本地化的消息内容","example":"权限不足，无法访问该资源","minLength":1,"maxLength":1000}},"additionalProperties":false,"x-module":"error","x-category":"错误处理"},"help":{"description":"错误的帮助信息，可选字段","type":"object","title":"Help","required":["desc","url"],"properties":{"desc":{"type":"string","description":"链接描述，说明该链接提供的帮助内容","example":"查看 API 使用指南","minLength":1,"maxLength":200},"url":{"type":"string","description":"帮助链接的 URL 地址","example":"https://docs.example.com/api-guide","format":"uri","maxLength":500}},"additionalProperties":false,"x-module":"error","x-category":"错误处理"}},"additionalProperties":false,"x-module":"error","x-category":"错误处理"}}}},"500":{"description":"Internal Server Error","content":{"*/*":{"schema":{"type":"object","title":"ApiError","description":"REST API 错误响应的主要数据结构，遵循 Google API 错误处理规范","required":["code","message","status","details"],"properties":{"code":{"type":"integer","description":"HTTP 状态码（数字），例如 403、404、500等","example":403,"minimum":100,"maximum":599},"message":{"type":"string","description":"面向开发者的错误消息，应该使用英文","example":"Permission denied","minLength":1,"maxLength":1000},"status":{"description":"RestAPI 对应的状态码枚举值","type":"string","title":"Code","enum":["OK","CANCELLED","UNKNOWN","INVALID_ARGUMENT","DEADLINE_EXCEEDED","NOT_FOUND","ALREADY_EXISTS","PERMISSION_DENIED","UNAUTHENTICATED","RESOURCE_EXHAUSTED","FAILED_PRECONDITION","ABORTED","OUT_OF_RANGE","UNIMPLEMENTED","INTERNAL","UNAVAILABLE","DATA_LOSS","PARTIAL_ELEMENT_UPDATE_FAILED"],"x-module":"error","x-category":"错误处理"},"details":{"description":"错误的详细信息","type":"object","title":"ErrorDetails","required":["reason"],"properties":{"reason":{"type":"string","description":"错误原因，标识错误的直接原因。格式为大写蛇形命名","example":"INVALID_REQUEST_FORMAT","pattern":"^[A-Z][A-Z0-9_]*[A-Z0-9]$","maxLength":63},"message":{"type":"string","description":"针对此错误发生的人类可读的解释说明","example":"请求参数格式不正确，缺少必需的字段 'name'","maxLength":500},"domain":{"type":"string","description":"错误域，表示错误原因所属的逻辑分组，通常是生成错误的服务名称","example":"deco.manycoreapis.com","maxLength":100},"metaData":{"type":"object","description":"关于此错误的附加结构化详细信息，键名限制为64个字符","additionalProperties":{"type":"string","maxLength":200},"example":{"maxAllowedSize":"1000","actualSize":"1500"}}},"additionalProperties":false,"x-module":"error","x-category":"错误处理"},"localizedMessage":{"description":"错误的本地化消息，可选字段","type":"object","title":"LocalizedMessage","required":["locale","message"],"properties":{"locale":{"type":"string","description":"消息所使用的语言环境","example":"zh-CN","pattern":"^[a-z]{2,3}(-[A-Z]{2})?(-[a-z0-9]{5,8})?(-[a-z0-9]{1,8})*$"},"message":{"type":"string","description":"本地化的消息内容","example":"权限不足，无法访问该资源","minLength":1,"maxLength":1000}},"additionalProperties":false,"x-module":"error","x-category":"错误处理"},"help":{"description":"错误的帮助信息，可选字段","type":"object","title":"Help","required":["desc","url"],"properties":{"desc":{"type":"string","description":"链接描述，说明该链接提供的帮助内容","example":"查看 API 使用指南","minLength":1,"maxLength":200},"url":{"type":"string","description":"帮助链接的 URL 地址","example":"https://docs.example.com/api-guide","format":"uri","maxLength":500}},"additionalProperties":false,"x-module":"error","x-category":"错误处理"}},"additionalProperties":false,"x-module":"error","x-category":"错误处理"}}}}},"x-return-extra":{"is-operation":"true"},"extensions":[{"key":"x-return-extra","value":{"is-operation":"true"}}],"method":"post","path":"/koolux/openapi/rest/v1/light:importdatasource","servers":[{"url":"https://www.koolux.com","description":"KooLux官网地址"}],"jsonRequestBodyExample":{"requestId":"string","operationId":"string","dataSourceId":"string","type":"UNSPECIFIED","bizTypeId":0},"info":{"title":"KooLux REST API","description":"KooLux照明设计的 REST API，提供照明模型、IES的全生命周期管理。  \n\n## 功能特性  \n- 支持IES文件的管理  \n- 支持照明模型管理  \n- 批量操作支持（创建、更新、删除、查询）  \n\n## 数据模型  \n- **照明模型**: 支持材质编辑和灯光模型排除  \n- **IES光源**: 包含IES文件全量信息\n","contact":{"name":"照明设计团队","email":"<EMAIL>"},"license":{"name":"内部使用","url":"https://www.manycore.com"},"version":"1.0.0"},"postman":{"name":"创建KooLux方案","description":{"content":"导入酷家乐方案到KooLux","type":"text/plain"},"url":{"path":["koolux","openapi","rest","v1","light:importdatasource"],"host":["{{baseUrl}}"],"query":[],"variable":[]},"header":[{"key":"Content-Type","value":"application/json"},{"key":"Accept","value":"application/json"}],"method":"POST","body":{"mode":"raw","raw":"","options":{"raw":{"language":"json"}}}}}
sidebar_class_name: "post api-method"
info_path: docs/api/koolux/koolux-rest-api
custom_edit_url: null
---

import MethodEndpoint from "@theme/ApiExplorer/MethodEndpoint";
import ParamsDetails from "@theme/ParamsDetails";
import RequestSchema from "@theme/RequestSchema";
import StatusCodes from "@theme/StatusCodes";
import OperationTabs from "@theme/OperationTabs";
import TabItem from "@theme/TabItem";
import Heading from "@theme/Heading";

<Heading
  as={"h1"}
  className={"openapi__heading"}
  children={"创建KooLux方案"}
>
</Heading>

<MethodEndpoint
  method={"post"}
  path={"/koolux/openapi/rest/v1/light:importdatasource"}
  context={"endpoint"}
>
  
</MethodEndpoint>



导入酷家乐方案到KooLux

<Heading
  id={"request"}
  as={"h2"}
  className={"openapi-tabs__heading"}
  children={"Request"}
>
</Heading>

<ParamsDetails
  parameters={undefined}
>
  
</ParamsDetails>

<RequestSchema
  title={"Body"}
  body={{"description":"create koolux design request data","content":{"application/json":{"schema":{"required":["bizTypeId","dataSourceId","type"],"type":"object","properties":{"requestId":{"type":"string","description":"请求ID，用于幂等性控制"},"operationId":{"type":"string","description":"操作ID，用于异步操作跟踪"},"dataSourceId":{"type":"string","description":"数据源ID（如酷家乐方案，则是酷家乐的designId）"},"type":{"type":"string","description":"数据源类型，UNSPECIFIED(未定义)，KJL(酷家乐)","enum":["UNSPECIFIED","KJL"]},"bizTypeId":{"type":"integer","description":"业务线ID，联系KooLux分配","format":"int32"}},"title":"CreateDesignRequest"}}},"required":true}}
>
  
</RequestSchema>

<StatusCodes
  id={undefined}
  label={undefined}
  responses={{"200":{"description":"Success","content":{"application/json":{"schema":{"type":"object","properties":{"dataSourceId":{"type":"string","description":"数据源ID（如酷家乐方案，则是酷家乐的designId）"},"type":{"type":"string","description":"数据源类型，UNSPECIFIED(未定义)，KJL(酷家乐)","enum":["UNSPECIFIED","KJL"]},"kooLuxDesignId":{"type":"string","description":"KooLux方案ID"}},"title":"CreateDesignResponse"}}}},"400":{"description":"Invalid Argument","content":{"*/*":{"schema":{"type":"object","title":"ApiError","description":"REST API 错误响应的主要数据结构，遵循 Google API 错误处理规范","required":["code","message","status","details"],"properties":{"code":{"type":"integer","description":"HTTP 状态码（数字），例如 403、404、500等","example":403,"minimum":100,"maximum":599},"message":{"type":"string","description":"面向开发者的错误消息，应该使用英文","example":"Permission denied","minLength":1,"maxLength":1000},"status":{"description":"RestAPI 对应的状态码枚举值","type":"string","title":"Code","enum":["OK","CANCELLED","UNKNOWN","INVALID_ARGUMENT","DEADLINE_EXCEEDED","NOT_FOUND","ALREADY_EXISTS","PERMISSION_DENIED","UNAUTHENTICATED","RESOURCE_EXHAUSTED","FAILED_PRECONDITION","ABORTED","OUT_OF_RANGE","UNIMPLEMENTED","INTERNAL","UNAVAILABLE","DATA_LOSS","PARTIAL_ELEMENT_UPDATE_FAILED"],"x-module":"error","x-category":"错误处理"},"details":{"description":"错误的详细信息","type":"object","title":"ErrorDetails","required":["reason"],"properties":{"reason":{"type":"string","description":"错误原因，标识错误的直接原因。格式为大写蛇形命名","example":"INVALID_REQUEST_FORMAT","pattern":"^[A-Z][A-Z0-9_]*[A-Z0-9]$","maxLength":63},"message":{"type":"string","description":"针对此错误发生的人类可读的解释说明","example":"请求参数格式不正确，缺少必需的字段 'name'","maxLength":500},"domain":{"type":"string","description":"错误域，表示错误原因所属的逻辑分组，通常是生成错误的服务名称","example":"deco.manycoreapis.com","maxLength":100},"metaData":{"type":"object","description":"关于此错误的附加结构化详细信息，键名限制为64个字符","additionalProperties":{"type":"string","maxLength":200},"example":{"maxAllowedSize":"1000","actualSize":"1500"}}},"additionalProperties":false,"x-module":"error","x-category":"错误处理"},"localizedMessage":{"description":"错误的本地化消息，可选字段","type":"object","title":"LocalizedMessage","required":["locale","message"],"properties":{"locale":{"type":"string","description":"消息所使用的语言环境","example":"zh-CN","pattern":"^[a-z]{2,3}(-[A-Z]{2})?(-[a-z0-9]{5,8})?(-[a-z0-9]{1,8})*$"},"message":{"type":"string","description":"本地化的消息内容","example":"权限不足，无法访问该资源","minLength":1,"maxLength":1000}},"additionalProperties":false,"x-module":"error","x-category":"错误处理"},"help":{"description":"错误的帮助信息，可选字段","type":"object","title":"Help","required":["desc","url"],"properties":{"desc":{"type":"string","description":"链接描述，说明该链接提供的帮助内容","example":"查看 API 使用指南","minLength":1,"maxLength":200},"url":{"type":"string","description":"帮助链接的 URL 地址","example":"https://docs.example.com/api-guide","format":"uri","maxLength":500}},"additionalProperties":false,"x-module":"error","x-category":"错误处理"}},"additionalProperties":false,"x-module":"error","x-category":"错误处理"}}}},"401":{"description":"Unauthenticated","content":{"*/*":{"schema":{"type":"object","title":"ApiError","description":"REST API 错误响应的主要数据结构，遵循 Google API 错误处理规范","required":["code","message","status","details"],"properties":{"code":{"type":"integer","description":"HTTP 状态码（数字），例如 403、404、500等","example":403,"minimum":100,"maximum":599},"message":{"type":"string","description":"面向开发者的错误消息，应该使用英文","example":"Permission denied","minLength":1,"maxLength":1000},"status":{"description":"RestAPI 对应的状态码枚举值","type":"string","title":"Code","enum":["OK","CANCELLED","UNKNOWN","INVALID_ARGUMENT","DEADLINE_EXCEEDED","NOT_FOUND","ALREADY_EXISTS","PERMISSION_DENIED","UNAUTHENTICATED","RESOURCE_EXHAUSTED","FAILED_PRECONDITION","ABORTED","OUT_OF_RANGE","UNIMPLEMENTED","INTERNAL","UNAVAILABLE","DATA_LOSS","PARTIAL_ELEMENT_UPDATE_FAILED"],"x-module":"error","x-category":"错误处理"},"details":{"description":"错误的详细信息","type":"object","title":"ErrorDetails","required":["reason"],"properties":{"reason":{"type":"string","description":"错误原因，标识错误的直接原因。格式为大写蛇形命名","example":"INVALID_REQUEST_FORMAT","pattern":"^[A-Z][A-Z0-9_]*[A-Z0-9]$","maxLength":63},"message":{"type":"string","description":"针对此错误发生的人类可读的解释说明","example":"请求参数格式不正确，缺少必需的字段 'name'","maxLength":500},"domain":{"type":"string","description":"错误域，表示错误原因所属的逻辑分组，通常是生成错误的服务名称","example":"deco.manycoreapis.com","maxLength":100},"metaData":{"type":"object","description":"关于此错误的附加结构化详细信息，键名限制为64个字符","additionalProperties":{"type":"string","maxLength":200},"example":{"maxAllowedSize":"1000","actualSize":"1500"}}},"additionalProperties":false,"x-module":"error","x-category":"错误处理"},"localizedMessage":{"description":"错误的本地化消息，可选字段","type":"object","title":"LocalizedMessage","required":["locale","message"],"properties":{"locale":{"type":"string","description":"消息所使用的语言环境","example":"zh-CN","pattern":"^[a-z]{2,3}(-[A-Z]{2})?(-[a-z0-9]{5,8})?(-[a-z0-9]{1,8})*$"},"message":{"type":"string","description":"本地化的消息内容","example":"权限不足，无法访问该资源","minLength":1,"maxLength":1000}},"additionalProperties":false,"x-module":"error","x-category":"错误处理"},"help":{"description":"错误的帮助信息，可选字段","type":"object","title":"Help","required":["desc","url"],"properties":{"desc":{"type":"string","description":"链接描述，说明该链接提供的帮助内容","example":"查看 API 使用指南","minLength":1,"maxLength":200},"url":{"type":"string","description":"帮助链接的 URL 地址","example":"https://docs.example.com/api-guide","format":"uri","maxLength":500}},"additionalProperties":false,"x-module":"error","x-category":"错误处理"}},"additionalProperties":false,"x-module":"error","x-category":"错误处理"}}}},"404":{"description":"Design not found","content":{"*/*":{"schema":{"type":"object","title":"ApiError","description":"REST API 错误响应的主要数据结构，遵循 Google API 错误处理规范","required":["code","message","status","details"],"properties":{"code":{"type":"integer","description":"HTTP 状态码（数字），例如 403、404、500等","example":403,"minimum":100,"maximum":599},"message":{"type":"string","description":"面向开发者的错误消息，应该使用英文","example":"Permission denied","minLength":1,"maxLength":1000},"status":{"description":"RestAPI 对应的状态码枚举值","type":"string","title":"Code","enum":["OK","CANCELLED","UNKNOWN","INVALID_ARGUMENT","DEADLINE_EXCEEDED","NOT_FOUND","ALREADY_EXISTS","PERMISSION_DENIED","UNAUTHENTICATED","RESOURCE_EXHAUSTED","FAILED_PRECONDITION","ABORTED","OUT_OF_RANGE","UNIMPLEMENTED","INTERNAL","UNAVAILABLE","DATA_LOSS","PARTIAL_ELEMENT_UPDATE_FAILED"],"x-module":"error","x-category":"错误处理"},"details":{"description":"错误的详细信息","type":"object","title":"ErrorDetails","required":["reason"],"properties":{"reason":{"type":"string","description":"错误原因，标识错误的直接原因。格式为大写蛇形命名","example":"INVALID_REQUEST_FORMAT","pattern":"^[A-Z][A-Z0-9_]*[A-Z0-9]$","maxLength":63},"message":{"type":"string","description":"针对此错误发生的人类可读的解释说明","example":"请求参数格式不正确，缺少必需的字段 'name'","maxLength":500},"domain":{"type":"string","description":"错误域，表示错误原因所属的逻辑分组，通常是生成错误的服务名称","example":"deco.manycoreapis.com","maxLength":100},"metaData":{"type":"object","description":"关于此错误的附加结构化详细信息，键名限制为64个字符","additionalProperties":{"type":"string","maxLength":200},"example":{"maxAllowedSize":"1000","actualSize":"1500"}}},"additionalProperties":false,"x-module":"error","x-category":"错误处理"},"localizedMessage":{"description":"错误的本地化消息，可选字段","type":"object","title":"LocalizedMessage","required":["locale","message"],"properties":{"locale":{"type":"string","description":"消息所使用的语言环境","example":"zh-CN","pattern":"^[a-z]{2,3}(-[A-Z]{2})?(-[a-z0-9]{5,8})?(-[a-z0-9]{1,8})*$"},"message":{"type":"string","description":"本地化的消息内容","example":"权限不足，无法访问该资源","minLength":1,"maxLength":1000}},"additionalProperties":false,"x-module":"error","x-category":"错误处理"},"help":{"description":"错误的帮助信息，可选字段","type":"object","title":"Help","required":["desc","url"],"properties":{"desc":{"type":"string","description":"链接描述，说明该链接提供的帮助内容","example":"查看 API 使用指南","minLength":1,"maxLength":200},"url":{"type":"string","description":"帮助链接的 URL 地址","example":"https://docs.example.com/api-guide","format":"uri","maxLength":500}},"additionalProperties":false,"x-module":"error","x-category":"错误处理"}},"additionalProperties":false,"x-module":"error","x-category":"错误处理"}}}},"500":{"description":"Internal Server Error","content":{"*/*":{"schema":{"type":"object","title":"ApiError","description":"REST API 错误响应的主要数据结构，遵循 Google API 错误处理规范","required":["code","message","status","details"],"properties":{"code":{"type":"integer","description":"HTTP 状态码（数字），例如 403、404、500等","example":403,"minimum":100,"maximum":599},"message":{"type":"string","description":"面向开发者的错误消息，应该使用英文","example":"Permission denied","minLength":1,"maxLength":1000},"status":{"description":"RestAPI 对应的状态码枚举值","type":"string","title":"Code","enum":["OK","CANCELLED","UNKNOWN","INVALID_ARGUMENT","DEADLINE_EXCEEDED","NOT_FOUND","ALREADY_EXISTS","PERMISSION_DENIED","UNAUTHENTICATED","RESOURCE_EXHAUSTED","FAILED_PRECONDITION","ABORTED","OUT_OF_RANGE","UNIMPLEMENTED","INTERNAL","UNAVAILABLE","DATA_LOSS","PARTIAL_ELEMENT_UPDATE_FAILED"],"x-module":"error","x-category":"错误处理"},"details":{"description":"错误的详细信息","type":"object","title":"ErrorDetails","required":["reason"],"properties":{"reason":{"type":"string","description":"错误原因，标识错误的直接原因。格式为大写蛇形命名","example":"INVALID_REQUEST_FORMAT","pattern":"^[A-Z][A-Z0-9_]*[A-Z0-9]$","maxLength":63},"message":{"type":"string","description":"针对此错误发生的人类可读的解释说明","example":"请求参数格式不正确，缺少必需的字段 'name'","maxLength":500},"domain":{"type":"string","description":"错误域，表示错误原因所属的逻辑分组，通常是生成错误的服务名称","example":"deco.manycoreapis.com","maxLength":100},"metaData":{"type":"object","description":"关于此错误的附加结构化详细信息，键名限制为64个字符","additionalProperties":{"type":"string","maxLength":200},"example":{"maxAllowedSize":"1000","actualSize":"1500"}}},"additionalProperties":false,"x-module":"error","x-category":"错误处理"},"localizedMessage":{"description":"错误的本地化消息，可选字段","type":"object","title":"LocalizedMessage","required":["locale","message"],"properties":{"locale":{"type":"string","description":"消息所使用的语言环境","example":"zh-CN","pattern":"^[a-z]{2,3}(-[A-Z]{2})?(-[a-z0-9]{5,8})?(-[a-z0-9]{1,8})*$"},"message":{"type":"string","description":"本地化的消息内容","example":"权限不足，无法访问该资源","minLength":1,"maxLength":1000}},"additionalProperties":false,"x-module":"error","x-category":"错误处理"},"help":{"description":"错误的帮助信息，可选字段","type":"object","title":"Help","required":["desc","url"],"properties":{"desc":{"type":"string","description":"链接描述，说明该链接提供的帮助内容","example":"查看 API 使用指南","minLength":1,"maxLength":200},"url":{"type":"string","description":"帮助链接的 URL 地址","example":"https://docs.example.com/api-guide","format":"uri","maxLength":500}},"additionalProperties":false,"x-module":"error","x-category":"错误处理"}},"additionalProperties":false,"x-module":"error","x-category":"错误处理"}}}}}}
>
  
</StatusCodes>


      