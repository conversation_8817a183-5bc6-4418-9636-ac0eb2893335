# GitLab CI/CD 配置指南

## 🎯 目标

配置 GitLab CI/CD 流水线，实现：
- 自动检测 API 变更
- 生成 Manual 网站文档预览
- 在 MR 中自动发布预览链接和评审检查清单
- 多环境自动部署支持

## 🔧 必需的环境变量配置

### 步骤1：创建 GitLab Personal Access Token

1. **登录 GitLab** → **用户设置** → **Access Tokens**
2. **创建新 Token**：
   - **Name**: `API Review Bot`
   - **Expiration**: 建议设置1年或更长
   - **Scopes**: 选择以下权限：
     - `api` - 用于添加 MR 评论
     - `read_repository` - 读取仓库信息
     - `write_repository` - 推送到仓库（如需要）
3. **复制生成的 Token**（只会显示一次）

### 步骤2：在项目中配置 CI 变量

进入项目 → **Settings** → **CI/CD** → **Variables**，添加以下变量：

#### 必需变量

| 变量名 | 值 | 说明 | 配置选项 |
|--------|----|----- |----------|
| `GITLAB_TOKEN` | 第1步生成的Token | 用于自动添加MR评论 | ✅ Protected, ✅ Masked |

#### Manual网站部署变量（可选）

如果需要部署到Manual网站，还需要配置：

| 变量名 | 值 | 说明 | 配置选项 |
|--------|----|----- |----------|
| `MANUAL_DEPLOY_TOKEN` | Manual网站部署Token | 用于部署到Manual网站 | ✅ Protected, ✅ Masked |
| `MANUAL_DEPLOY_URL` | `https://manual.qunhequnhe.com/api/deploy` | Manual网站部署API地址 | ✅ Protected |
| `MANUAL_BASE_URL` | `https://manual.qunhequnhe.com` | Manual网站基础URL | - |

#### Docker Registry 变量（可选）

如需要使用私有Docker镜像：

| 变量名 | 值 | 说明 | 配置选项 |
|--------|----|----- |----------|
| `CI_REGISTRY_USER` | Docker Registry用户名 | - | ✅ Protected |
| `CI_REGISTRY_PASSWORD` | Docker Registry密码 | - | ✅ Protected, ✅ Masked |

### 步骤3：验证CI/CD Runner配置

确保GitLab Runner已正确配置并可以执行以下任务：
- Docker 容器运行
- Node.js 环境支持
- 网络访问权限（用于部署到Manual网站）

## 🚀 功能特性

配置完成后，CI/CD流水线将自动提供以下功能：

### 1. API变更自动检测
- 自动检测 `openapi/` 目录下的YAML文件变更
- 设置 `HAS_API_CHANGES` 变量控制后续流程

### 2. 自动文档生成和部署
- 动态生成OpenAPI配置和Docusaurus内容
- 部署到多个环境：
  - **开发环境**: `manycoreapi-demo` (特性分支)
  - **预发布环境**: `manycoreapi-staging` (master/main分支)
  - **生产环境**: `manycoreapi` (Tag版本)

### 3. 智能MR评论
当检测到API变更时，自动在MR中添加评论，包含：
- 🌐 完整的Manual网站预览链接
- 📋 具体API服务的文档链接
- 📄 变更文件列表
- ✅ 标准化评审检查清单
- 🔧 环境和版本信息

## 📖 评审者使用指南

### 方法1：Manual网站预览（推荐）
1. 在MR中查看CI自动添加的评论
2. 点击 **"查看Manual网站"** 链接
3. 享受完整的在线文档体验：
   - 交互式API测试
   - 响应式设计
   - 实时同步更新

### 方法2：GitLab Environments
1. 在MR页面点击 **"Environments"** 标签
2. 查看部署状态和访问链接
3. 点击对应环境的链接访问文档


## 🔄 CI/CD流程详解

### Stage 1: validate_api_changes
```yaml
validate_api_changes:
  stage: validate
  script:
    - scripts/ci/validation/detect-api-changes.sh
  artifacts:
    reports:
      dotenv: api-changes.env
```
- 检测API文件变更
- 设置 `HAS_API_CHANGES` 环境变量

### Stage 2: validate_specs
```yaml
validate_specs:
  stage: validate
  rules:
    - if: '$HAS_API_CHANGES == "true"'
  script:
    - scripts/ci/validation/validate-specs.sh
```
- 只在有API变更时执行
- 使用Spectral进行OpenAPI规范校验

### Stage 3: 文档生成和部署
```yaml
generate_all_dynamic_content:
  stage: generate
  script:
    - node scripts/generate-openapi-configs.js
    - node scripts/generate-docs-content.js

deploy_manual_dev:
  stage: deploy
  script:
    - scripts/ci/docs/deploy-to-manual.sh
  environment:
    name: manual-dev
    url: https://manual.qunhequnhe.com/manycoreapi-demo/$PREVIEW_VERSION/
```

### Stage 4: 自动评论
```yaml
add_mr_comment:
  stage: comment
  rules:
    - if: '$CI_MERGE_REQUEST_IID && $HAS_API_CHANGES == "true"'
  script:
    - scripts/add-mr-comment.sh
```
- 只在MR中且有API变更时执行
- 自动添加包含预览链接的评论

## 🌍 多环境部署策略

### 环境配置

| 环境 | 分支条件 | 部署路径 | 版本格式 |
|------|----------|----------|----------|
| 开发环境 | 特性分支 | `manycoreapi-demo` | `0.0.1-{branch}-{commit}` |
| 预发布环境 | master/main | `manycoreapi-staging` | `1.0.0-{timestamp}` |
| 生产环境 | Git Tag | `manycoreapi` | `{tag}` |

### 版本号生成逻辑
```bash
if [[ -n "$CI_COMMIT_TAG" ]]; then
    PREVIEW_VERSION="$CI_COMMIT_TAG"
    BASE_PATH="manycoreapi"
elif [[ "$CI_COMMIT_BRANCH" == "master" ]] || [[ "$CI_COMMIT_BRANCH" == "main" ]]; then
    PREVIEW_VERSION="1.0.0-$(date +%Y%m%d%H%M%S)"
    BASE_PATH="manycoreapi-staging"
else
    BRANCH_NAME=$(echo "$CI_COMMIT_BRANCH" | sed 's/[^a-zA-Z0-9.-]/-/g')
    PREVIEW_VERSION="0.0.1-${BRANCH_NAME}-${CI_COMMIT_SHORT_SHA}"
    BASE_PATH="manycoreapi-demo"
fi
```

## 🔍 评审流程和检查清单

### 自动生成的评审检查清单：
- [ ] API 设计符合 RESTful 原则
- [ ] 新增/修改的端点文档完整
- [ ] 请求/响应参数说明清晰
- [ ] 错误响应格式符合标准
- [ ] 示例代码准确有效
- [ ] 安全性考虑充分（认证、授权、数据验证）
- [ ] 向后兼容性评估完成
- [ ] 性能影响评估（如有必要）

### 评审流程：
1. **查看预览** → **使用检查清单** → **提供反馈** → **更新标签**
2. **迭代修改** → **重新部署** → **再次评审** → **最终通过**

## 🛠️ 故障排除

### 自动评论未生成
**可能原因和解决方案：**
```bash
# 检查环境变量
echo "GITLAB_TOKEN设置: $([ -n "$GITLAB_TOKEN" ] && echo "✅" || echo "❌")"
echo "HAS_API_CHANGES: $HAS_API_CHANGES"
echo "CI_MERGE_REQUEST_IID: $CI_MERGE_REQUEST_IID"

# 手动执行评论脚本进行调试
scripts/add-mr-comment.sh
```

### Manual网站预览失败
**检查步骤：**
1. 验证动态内容生成是否成功
2. 检查Docusaurus构建日志
3. 确认Manual网站部署状态
4. 验证网络连接和权限

### API变更检测失败
**常见问题：**
```bash
# 检查文件路径
ls -la openapi/*/restapi.yaml

# 手动执行检测脚本
scripts/ci/validation/detect-api-changes.sh

# 查看生成的环境变量
cat api-changes.env
```

### Docker权限问题
```bash
# 确保Docker服务可用
docker info

# 检查Registry登录
docker login $CI_REGISTRY -u $CI_REGISTRY_USER -p $CI_REGISTRY_PASSWORD
```

## 🔐 安全最佳实践

### Token安全
- ✅ 所有敏感Token标记为 `Masked` 和 `Protected`
- ✅ 定期轮换Access Token（建议每6个月）
- ✅ 只授予必要的最小权限
- ⚠️ 如果Token泄露，立即撤销并重新生成

### CI/CD安全
- 使用受保护的分支规则
- 限制部署权限到特定用户/角色
- 启用流水线审批机制（生产环境）
- 定期审查CI/CD变量和权限

## 📞 技术支持

### 联系方式
- **CI/CD问题**: [DevOps团队]
- **API评审流程**: [后端API小组]
- **Manual网站**: [文档平台团队]

### 有用的调试命令
```bash
# 查看CI环境变量
env | grep CI_

# 测试GitLab API连接
curl -H "Private-Token: $GITLAB_TOKEN" \
  "$CI_API_V4_URL/projects/$CI_PROJECT_ID"

# 检查Manual网站部署状态
curl -I "https://manual.qunhequnhe.com/manycoreapi-demo/"
```

## 🔄 版本更新日志

### v2.0 (当前版本)
- ✨ 新增Manual网站自动部署
- ✨ 智能MR评论系统
- ✨ 多环境部署支持
- ✨ 标准化评审检查清单
- 🔧 改进的动态内容生成

### v1.0 (历史版本)  
- 基础的GitLab Pages文档生成
- 简单的Artifacts下载方式 