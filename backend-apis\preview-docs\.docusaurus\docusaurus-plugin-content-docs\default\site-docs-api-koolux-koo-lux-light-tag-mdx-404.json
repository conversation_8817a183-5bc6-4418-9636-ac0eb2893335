{"id": "api/koolux/koo-lux-light", "title": "KooLuxLight", "description": "KooLuxLight", "source": "@site/docs/api/koolux/koo-lux-light.tag.mdx", "sourceDirName": "api/koolux", "slug": "/api/koolux/koo-lux-light", "permalink": "/manycoreapi-demo/0.0.4/docs/api/koolux/koo-lux-light", "draft": false, "unlisted": false, "editUrl": null, "tags": [], "version": "current", "frontMatter": {"id": "koo-lux-light", "title": "KooLuxLight", "description": "KooLuxLight", "custom_edit_url": null}, "sidebar": "kooluxSidebar", "previous": {"title": "Introduction", "permalink": "/manycoreapi-demo/0.0.4/docs/api/koolux/koolux-rest-api"}, "next": {"title": "创建KooLux方案", "permalink": "/manycoreapi-demo/0.0.4/docs/api/koolux/import-data-source"}}