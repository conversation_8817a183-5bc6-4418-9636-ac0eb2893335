# Docker 镜像拉取优化方案

## 问题描述

GitLab CI 在拉取 Docker 镜像时出现超时错误：
```
Error response from daemon: Head "https://registry-qunhe.qunhequnhe.com/v2/display/openapi-generator/manifests/latest": Get "https://registry-qunhe02.qunhequnhe.com/service/token?scope=repository%3Adisplay%2Fopenapi-generator%3Apull&service=harbor-registry": net/http: request canceled while waiting for connection (Client.Timeout exceeded while awaiting headers)
```

## 优化措施

### 1. 镜像拉取重试机制

**文件**: `tools/ci-cd/ci/common/docker-image-pull.sh`

- **智能重试**: 支持配置重试次数、延迟时间和超时时间
- **本地缓存检查**: 优先使用本地已存在的镜像
- **备用镜像支持**: 主镜像失败时自动切换到备用镜像
- **详细日志**: 提供彩色输出和详细的错误信息

**配置参数**:
```bash
IMAGE_PULL_RETRIES=3      # 重试次数
IMAGE_PULL_DELAY=30       # 重试间隔（秒）
DOCKER_PULL_TIMEOUT=600   # 单次拉取超时（秒）
```

### 2. 镜像仓库健康检查

**文件**: `tools/ci-cd/ci/common/registry-health-check.sh`

- **连通性检查**: 在 CI 开始前检查镜像仓库是否可访问
- **镜像存在性验证**: 确认所需镜像是否存在
- **镜像信息获取**: 显示镜像的详细信息
- **多仓库支持**: 同时检查主仓库和备用仓库

### 3. GitLab CI 配置优化

**文件**: `backend-apis/.gitlab-ci.yml`

#### 3.1 全局配置优化
```yaml
# Docker 服务配置
services:
  - name: docker:dind
    command: ["--registry-mirror=https://registry-qunhe.qunhequnhe.com"]

# 优化的环境变量
variables:
  DOCKER_PULL_TIMEOUT: "600"
  DOCKER_PULL_POLICY: "if-not-present"
  IMAGE_PULL_RETRIES: "3"
  IMAGE_PULL_DELAY: "30"
  DOCKER_DEBUG: "false"
```

#### 3.2 全局 before_script
```yaml
default:
  before_script:
    - chmod +x tools/ci-cd/ci/common/docker-image-pull.sh
    - source tools/ci-cd/ci/common/docker-image-pull.sh
```

#### 3.3 新增健康检查阶段
```yaml
stages:
  - health_check  # 新增
  - mr_pipeline
  - generate_sdk
  - build
  - test
  - deploy
```

#### 3.4 重试策略优化
```yaml
retry:
  max: 2
  when:
    - runner_system_failure
    - stuck_or_timeout_failure
    - api_failure
    - scheduler_failure
    - job_execution_timeout  # 新增超时重试
```

### 4. 特定 Job 的优化配置

对于关键的 job（如 `mr_complete_pipeline`），提供了更激进的重试配置：

```yaml
variables:
  IMAGE_PULL_RETRIES: "5"     # 更多重试次数
  IMAGE_PULL_DELAY: "60"      # 更长延迟
  DOCKER_PULL_TIMEOUT: "900"  # 更长超时（15分钟）
```

## 使用方法

### 1. 自动使用
所有的优化措施都会自动生效，无需额外配置。

### 2. 调试模式
如果需要查看详细的镜像拉取日志，可以设置：
```yaml
variables:
  DOCKER_DEBUG: "true"
```

### 3. 手动健康检查
可以在本地运行健康检查脚本：
```bash
chmod +x tools/ci-cd/ci/common/registry-health-check.sh
./tools/ci-cd/ci/common/registry-health-check.sh
```

### 4. 手动镜像拉取
可以在本地测试镜像拉取脚本：
```bash
chmod +x tools/ci-cd/ci/common/docker-image-pull.sh
./tools/ci-cd/ci/common/docker-image-pull.sh
```

## 监控和故障排除

### 1. 查看 CI 日志
在 GitLab CI 的日志中，你会看到：
- 🔍 镜像可用性检查
- 📊 重试进度和结果
- ✅ 成功使用的镜像
- ⚠️ 警告和错误信息

### 2. 常见问题排查

**问题**: 镜像拉取仍然失败
**解决方案**:
1. 检查网络连接
2. 验证镜像仓库认证
3. 确认镜像名称和标签正确
4. 增加重试次数和超时时间

**问题**: 健康检查失败但 CI 继续执行
**解决方案**: 这是正常的，健康检查设置为 `allow_failure: true`，不会阻塞流程

### 3. 性能监控
- 监控镜像拉取时间
- 跟踪重试频率
- 观察备用镜像使用情况

## 后续改进建议

1. **镜像缓存策略**: 考虑在 Runner 上实现更持久的镜像缓存
2. **多区域镜像仓库**: 部署地理位置更近的镜像仓库
3. **镜像大小优化**: 减小镜像体积以加快拉取速度
4. **监控告警**: 设置镜像拉取失败的告警机制
