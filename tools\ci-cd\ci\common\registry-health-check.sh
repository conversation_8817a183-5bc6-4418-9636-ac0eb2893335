#!/bin/bash

# Docker 镜像仓库健康检查脚本
# 用于在 CI 开始前检查镜像仓库的可用性

set -e

# 颜色输出
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

log_info() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

log_warn() {
    echo -e "${YELLOW}[WARN]${NC} $1"
}

log_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

log_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

# 检查镜像仓库连通性
check_registry_connectivity() {
    local registry_url=$1
    local timeout=${2:-10}
    
    log_info "检查镜像仓库连通性: $registry_url"
    
    # 尝试连接到镜像仓库
    if timeout $timeout curl -s --fail --head "https://$registry_url/v2/" >/dev/null 2>&1; then
        log_success "镜像仓库连通性正常: $registry_url"
        return 0
    else
        log_error "镜像仓库连通性失败: $registry_url"
        return 1
    fi
}

# 检查镜像是否存在
check_image_exists() {
    local image=$1
    local timeout=${2:-30}
    
    log_info "检查镜像是否存在: $image"
    
    # 使用 docker manifest inspect 检查镜像
    if timeout $timeout docker manifest inspect "$image" >/dev/null 2>&1; then
        log_success "镜像存在: $image"
        return 0
    else
        log_error "镜像不存在或无法访问: $image"
        return 1
    fi
}

# 获取镜像信息
get_image_info() {
    local image=$1
    
    log_info "获取镜像信息: $image"
    
    # 获取镜像的详细信息
    if docker manifest inspect "$image" 2>/dev/null | jq -r '.mediaType, .schemaVersion' 2>/dev/null; then
        log_success "成功获取镜像信息"
        return 0
    else
        log_warn "无法获取镜像详细信息，但镜像可能仍然可用"
        return 1
    fi
}

# 主健康检查函数
main() {
    local primary_registry="registry.qunhequnhe.com"
    local fallback_registry="registry.qunhequnhe.com"
    local primary_image="${DOCKER_IMAGE:-registry.qunhequnhe.com/display/openapi-generator:latest}"
    local fallback_image="${FALLBACK_IMAGE:-}"

    log_info "=== Docker 镜像仓库健康检查（仅 qunhe registry）==="

    # 验证镜像来源，确保只使用 qunhe registry
    if [[ ! "$primary_image" =~ ^registry\.qunhequnhe\.com/ ]]; then
        log_error "主镜像不是来自 qunhe registry: $primary_image"
        return 1
    fi

    if [ -n "$fallback_image" ] && [[ ! "$fallback_image" =~ ^registry\.qunhequnhe\.com/ ]]; then
        log_error "备用镜像不是来自 qunhe registry: $fallback_image"
        return 1
    fi
    
    # 检查主镜像仓库
    local primary_registry_ok=false
    if check_registry_connectivity "$primary_registry"; then
        primary_registry_ok=true
    fi
    
    # 检查备用镜像仓库（如果存在）
    local fallback_registry_ok=false
    if [ -n "$fallback_image" ]; then
        if check_registry_connectivity "$fallback_registry"; then
            fallback_registry_ok=true
        fi
    fi
    
    # 如果两个仓库都不可用，给出警告
    if [ "$primary_registry_ok" = false ] && [ "$fallback_registry_ok" = false ]; then
        log_error "所有镜像仓库都不可用！"
        log_error "这可能导致镜像拉取失败"
        return 1
    fi
    
    # 检查主镜像
    local primary_image_ok=false
    if [ "$primary_registry_ok" = true ]; then
        if check_image_exists "$primary_image"; then
            primary_image_ok=true
            get_image_info "$primary_image" || true
        fi
    fi
    
    # 检查备用镜像（如果存在）
    local fallback_image_ok=false
    if [ -n "$fallback_image" ] && [ "$fallback_registry_ok" = true ]; then
        if check_image_exists "$fallback_image"; then
            fallback_image_ok=true
            get_image_info "$fallback_image" || true
        fi
    fi
    
    # 总结检查结果
    log_info "=== 健康检查结果 ==="
    log_info "主镜像仓库 ($primary_registry): $([ "$primary_registry_ok" = true ] && echo "✅ 正常" || echo "❌ 异常")"
    log_info "主镜像 ($primary_image): $([ "$primary_image_ok" = true ] && echo "✅ 可用" || echo "❌ 不可用")"
    
    if [ -n "$fallback_image" ]; then
        log_info "备用镜像仓库 ($fallback_registry): $([ "$fallback_registry_ok" = true ] && echo "✅ 正常" || echo "❌ 异常")"
        log_info "备用镜像 ($fallback_image): $([ "$fallback_image_ok" = true ] && echo "✅ 可用" || echo "❌ 不可用")"
    fi
    
    # 如果至少有一个镜像可用，返回成功
    if [ "$primary_image_ok" = true ] || [ "$fallback_image_ok" = true ]; then
        log_success "至少有一个镜像可用，CI 可以继续执行"
        return 0
    else
        log_error "没有可用的镜像，CI 可能会失败"
        return 1
    fi
}

# 如果脚本被直接执行
if [ "${BASH_SOURCE[0]}" == "${0}" ]; then
    main "$@"
fi
