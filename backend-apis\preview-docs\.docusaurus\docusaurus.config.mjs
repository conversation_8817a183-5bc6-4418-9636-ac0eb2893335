/*
 * AUTOGENERATED - DON'T EDIT
 * Your edits in this file will be overwritten in the next build!
 * Modify the docusaurus.config.js file at your site's root instead.
 */
export default {
  "title": "群核科技 API 文档中心",
  "tagline": "全栈家居云设计平台 API 参考",
  "favicon": "img/logo.svg",
  "url": "https://manual.qunhequnhe.com",
  "baseUrl": "/manycoreapi-demo/0.0.4/",
  "organizationName": "your-organization",
  "projectName": "backend-api-sdk",
  "onBrokenLinks": "ignore",
  "onBrokenMarkdownLinks": "ignore",
  "future": {
    "experimental_faster": {
      "swcJsLoader": false,
      "swcJsMinimizer": false,
      "swcHtmlMinimizer": false,
      "lightningCssMinimizer": false,
      "mdxCrossCompilerCache": false,
      "rspackBundler": false,
      "rspackPersistentCache": false,
      "ssgWorkerThreads": false
    },
    "v4": {
      "removeLegacyPostBuildHeadAttribute": true,
      "useCssCascadeLayers": true
    },
    "experimental_storage": {
      "type": "localStorage",
      "namespace": false
    },
    "experimental_router": "browser"
  },
  "plugins": [
    [
      "docusaurus-plugin-openapi-docs",
      {
        "id": "api",
        "docsPluginId": "classic",
        "config": {
          "camera": {
            "specPath": "../../specifications/services/camera/openapi.yaml",
            "outputDir": "docs/api/camera",
            "sidebarOptions": {
              "groupPathsBy": "tag",
              "categoryLinkSource": "tag"
            },
            "downloadUrl": "/specifications/services/camera/openapi.yaml",
            "hideSendButton": false,
            "showSchemas": false,
            "disableCompression": false
          },
          "doorwindow": {
            "specPath": "../../specifications/services/doorwindow/openapi.yaml",
            "outputDir": "docs/api/doorwindow",
            "sidebarOptions": {
              "groupPathsBy": "tag",
              "categoryLinkSource": "tag"
            },
            "downloadUrl": "/specifications/services/doorwindow/openapi.yaml",
            "hideSendButton": false,
            "showSchemas": false,
            "disableCompression": false
          },
          "furniture": {
            "specPath": "../../specifications/services/furniture/openapi.yaml",
            "outputDir": "docs/api/furniture",
            "sidebarOptions": {
              "groupPathsBy": "tag",
              "categoryLinkSource": "tag"
            },
            "downloadUrl": "/specifications/services/furniture/openapi.yaml",
            "hideSendButton": false,
            "showSchemas": false,
            "disableCompression": false
          },
          "koolux": {
            "specPath": "../../specifications/services/koolux/openapi.yaml",
            "outputDir": "docs/api/koolux",
            "sidebarOptions": {
              "groupPathsBy": "tag",
              "categoryLinkSource": "tag"
            },
            "downloadUrl": "/specifications/services/koolux/openapi.yaml",
            "hideSendButton": false,
            "showSchemas": false,
            "disableCompression": false
          },
          "layout": {
            "specPath": "../../specifications/services/layout/openapi.yaml",
            "outputDir": "docs/api/layout",
            "sidebarOptions": {
              "groupPathsBy": "tag",
              "categoryLinkSource": "tag"
            },
            "downloadUrl": "/specifications/services/layout/openapi.yaml",
            "hideSendButton": false,
            "showSchemas": false,
            "disableCompression": false
          },
          "pdm": {
            "specPath": "../../specifications/services/pdm/openapi.yaml",
            "outputDir": "docs/api/pdm",
            "sidebarOptions": {
              "groupPathsBy": "tag",
              "categoryLinkSource": "tag"
            },
            "downloadUrl": "/specifications/services/pdm/openapi.yaml",
            "hideSendButton": false,
            "showSchemas": false,
            "disableCompression": false
          }
        }
      }
    ]
  ],
  "markdown": {
    "mermaid": true,
    "format": "mdx",
    "mdx1Compat": {
      "comments": true,
      "admonitions": true,
      "headingIds": true
    },
    "anchors": {
      "maintainCase": false
    }
  },
  "i18n": {
    "defaultLocale": "zh-Hans",
    "locales": [
      "zh-Hans"
    ],
    "path": "i18n",
    "localeConfigs": {}
  },
  "presets": [
    [
      "classic",
      {
        "docs": {
          "sidebarPath": "./sidebars.ts",
          "docItemComponent": "@theme/ApiItem",
          "showLastUpdateTime": false,
          "showLastUpdateAuthor": false,
          "exclude": [
            "**/_*.{js,jsx,ts,tsx,md,mdx}",
            "**/_*/**",
            "**/*.test.{js,jsx,ts,tsx}",
            "**/__tests__/**"
          ],
          "includeCurrentVersion": true,
          "breadcrumbs": false,
          "remarkPlugins": [],
          "rehypePlugins": []
        },
        "blog": false,
        "theme": {
          "customCss": "./src/css/custom.css"
        }
      }
    ]
  ],
  "themes": [
    "docusaurus-theme-openapi-docs"
  ],
  "themeConfig": {
    "image": "img/docusaurus-social-card.jpg",
    "navbar": {
      "title": "群核科技 API",
      "logo": {
        "alt": "群核科技 Logo",
        "src": "img/logo.svg"
      },
      "hideOnScroll": true,
      "style": "primary",
      "items": [
        {
          "type": "docSidebar",
          "sidebarId": "tutorialSidebar",
          "position": "left",
          "label": "开发指南"
        },
        {
          "type": "dropdown",
          "label": "API 参考",
          "position": "left",
          "items": [
            {
              "type": "html",
              "value": "<hr style=\"margin: 0.5rem 0;\">"
            },
            {
              "type": "docSidebar",
              "sidebarId": "cameraSidebar",
              "label": "🏠 Camera Infrastructure API"
            },
            {
              "type": "docSidebar",
              "sidebarId": "doorwindowSidebar",
              "label": "🏠 门窗设计管理API"
            },
            {
              "type": "docSidebar",
              "sidebarId": "furnitureSidebar",
              "label": "🏠 家具设计管理API"
            },
            {
              "type": "docSidebar",
              "sidebarId": "kooluxSidebar",
              "label": "🏠 KooLux REST API"
            },
            {
              "type": "docSidebar",
              "sidebarId": "layoutSidebar",
              "label": "🏠 户型图例管理API"
            },
            {
              "type": "docSidebar",
              "sidebarId": "pdmSidebar",
              "label": "🏠 平面造型设计API"
            }
          ]
        },
        {
          "to": "/blog",
          "label": "📝 更新日志",
          "position": "left"
        },
        {
          "type": "search",
          "position": "right"
        },
        {
          "type": "localeDropdown",
          "position": "right",
          "dropdownItemsBefore": [],
          "dropdownItemsAfter": []
        },
        {
          "href": "https://developers.qunheco.com",
          "label": "开发者控制台",
          "position": "right",
          "className": "header-github-link"
        },
        {
          "href": "https://gitlab.com/your-org/backend-api-sdk",
          "label": "GitLab",
          "position": "right"
        }
      ]
    },
    "footer": {
      "style": "dark",
      "logo": {
        "alt": "群核科技 Logo",
        "src": "img/logo.svg",
        "href": "https://www.kujiale.com",
        "width": 32,
        "height": 32
      },
      "links": [
        {
          "title": "📚 产品文档",
          "items": [
            {
              "label": "快速开始",
              "to": "/docs/intro"
            },
            {
              "label": "API 概览",
              "to": "/docs/api"
            },
            {
              "label": "最佳实践",
              "to": "/docs/guides/best-practices"
            },
            {
              "label": "SDK 开发包",
              "to": "/docs/guides/sdks"
            }
          ]
        },
        {
          "title": "🔗 API 服务",
          "items": [
            {
              "label": "门窗服务",
              "to": "/docs/api/diymodeldw-service"
            },
            {
              "label": "家具设计服务",
              "to": "/docs/api/furniture-design-service"
            },
            {
              "label": "设计信息服务",
              "to": "/docs/api/designinfoservice"
            }
          ]
        },
        {
          "title": "🌐 开发者社区",
          "items": [
            {
              "label": "开发者控制台",
              "href": "https://developers.qunheco.com"
            },
            {
              "label": "技术博客",
              "href": "https://tech.qunheco.com"
            },
            {
              "label": "开发者论坛",
              "href": "https://forum.qunheco.com"
            },
            {
              "label": "GitHub",
              "href": "https://github.com/qunhe"
            }
          ]
        },
        {
          "title": "📞 支持与帮助",
          "items": [
            {
              "label": "更新日志",
              "to": "/blog"
            },
            {
              "label": "服务状态",
              "href": "https://status.qunheco.com"
            },
            {
              "label": "技术支持",
              "href": "mailto:<EMAIL>"
            },
            {
              "label": "GitLab 仓库",
              "href": "https://gitlab.com/your-org/backend-api-sdk"
            }
          ]
        }
      ],
      "copyright": "\n        <div style=\"margin-top: 1rem; padding-top: 1rem; border-top: 1px solid #334155;\">\n          <p>Copyright © 2025 群核科技有限公司. All rights reserved.</p>\n          <p style=\"font-size: 0.875rem; opacity: 0.8;\">\n            Built with ❤️ by Qunhe Technology Developer Team | Powered by Docusaurus\n          </p>\n        </div>\n      "
    },
    "prism": {
      "theme": {
        "plain": {
          "color": "#393A34",
          "backgroundColor": "#f6f8fa"
        },
        "styles": [
          {
            "types": [
              "comment",
              "prolog",
              "doctype",
              "cdata"
            ],
            "style": {
              "color": "#999988",
              "fontStyle": "italic"
            }
          },
          {
            "types": [
              "namespace"
            ],
            "style": {
              "opacity": 0.7
            }
          },
          {
            "types": [
              "string",
              "attr-value"
            ],
            "style": {
              "color": "#e3116c"
            }
          },
          {
            "types": [
              "punctuation",
              "operator"
            ],
            "style": {
              "color": "#393A34"
            }
          },
          {
            "types": [
              "entity",
              "url",
              "symbol",
              "number",
              "boolean",
              "variable",
              "constant",
              "property",
              "regex",
              "inserted"
            ],
            "style": {
              "color": "#36acaa"
            }
          },
          {
            "types": [
              "atrule",
              "keyword",
              "attr-name",
              "selector"
            ],
            "style": {
              "color": "#00a4db"
            }
          },
          {
            "types": [
              "function",
              "deleted",
              "tag"
            ],
            "style": {
              "color": "#d73a49"
            }
          },
          {
            "types": [
              "function-variable"
            ],
            "style": {
              "color": "#6f42c1"
            }
          },
          {
            "types": [
              "tag",
              "selector",
              "keyword"
            ],
            "style": {
              "color": "#00009f"
            }
          }
        ]
      },
      "darkTheme": {
        "plain": {
          "color": "#F8F8F2",
          "backgroundColor": "#282A36"
        },
        "styles": [
          {
            "types": [
              "prolog",
              "constant",
              "builtin"
            ],
            "style": {
              "color": "rgb(189, 147, 249)"
            }
          },
          {
            "types": [
              "inserted",
              "function"
            ],
            "style": {
              "color": "rgb(80, 250, 123)"
            }
          },
          {
            "types": [
              "deleted"
            ],
            "style": {
              "color": "rgb(255, 85, 85)"
            }
          },
          {
            "types": [
              "changed"
            ],
            "style": {
              "color": "rgb(255, 184, 108)"
            }
          },
          {
            "types": [
              "punctuation",
              "symbol"
            ],
            "style": {
              "color": "rgb(248, 248, 242)"
            }
          },
          {
            "types": [
              "string",
              "char",
              "tag",
              "selector"
            ],
            "style": {
              "color": "rgb(255, 121, 198)"
            }
          },
          {
            "types": [
              "keyword",
              "variable"
            ],
            "style": {
              "color": "rgb(189, 147, 249)",
              "fontStyle": "italic"
            }
          },
          {
            "types": [
              "comment"
            ],
            "style": {
              "color": "rgb(98, 114, 164)"
            }
          },
          {
            "types": [
              "attr-name"
            ],
            "style": {
              "color": "rgb(241, 250, 140)"
            }
          }
        ]
      },
      "additionalLanguages": [
        "bash",
        "diff",
        "json"
      ],
      "magicComments": [
        {
          "className": "theme-code-block-highlighted-line",
          "line": "highlight-next-line",
          "block": {
            "start": "highlight-start",
            "end": "highlight-end"
          }
        }
      ]
    },
    "algolia": {
      "appId": "YOUR_APP_ID",
      "apiKey": "YOUR_SEARCH_API_KEY",
      "indexName": "YOUR_INDEX_NAME",
      "contextualSearch": true,
      "searchParameters": {},
      "searchPagePath": "search"
    },
    "colorMode": {
      "defaultMode": "light",
      "disableSwitch": false,
      "respectPrefersColorScheme": false
    },
    "docs": {
      "versionPersistence": "localStorage",
      "sidebar": {
        "hideable": false,
        "autoCollapseCategories": false
      }
    },
    "blog": {
      "sidebar": {
        "groupByYear": true
      }
    },
    "metadata": [],
    "tableOfContents": {
      "minHeadingLevel": 2,
      "maxHeadingLevel": 3
    }
  },
  "baseUrlIssueBanner": true,
  "onBrokenAnchors": "warn",
  "onDuplicateRoutes": "warn",
  "staticDirectories": [
    "static"
  ],
  "customFields": {},
  "scripts": [],
  "headTags": [],
  "stylesheets": [],
  "clientModules": [],
  "titleDelimiter": "|",
  "noIndex": false
};
