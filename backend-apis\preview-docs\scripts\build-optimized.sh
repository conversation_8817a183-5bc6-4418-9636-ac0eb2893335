#!/bin/bash

# 优化构建脚本 - 减少Webpack序列化警告

echo "🚀 启动优化构建..."

# 设置环境变量
export NODE_OPTIONS="--max-old-space-size=8192"
export GENERATE_SOURCEMAP="false"
export DISABLE_ESLINT_PLUGIN="true"
export WEBPACK_CACHE_TYPE="filesystem"

# 清理缓存
echo "🧹 清理旧缓存..."
rm -rf .docusaurus/
rm -rf build/
rm -rf node_modules/.cache/

# 安装依赖
echo "📦 安装依赖..."
if [ -f "package-lock.json" ]; then
    npm ci --prefer-offline --no-audit --no-fund
else
    npm install --prefer-offline --no-audit --no-fund
fi

# 检查大型API文档
echo "🔍 检查API文档大小..."
if [ -d "docs/api" ]; then
    echo "发现的大型API文档："
    find docs/api -name "*.mdx" -exec sh -c 'size=$(wc -c < "$1"); if [ $size -gt 1000000 ]; then echo "  📄 $1: $(echo $size | numfmt --to=iec)"; fi' _ {} \;
fi

# 生成OpenAPI文档
echo "📖 生成OpenAPI文档..."
npm run gen-api-docs -- all || echo "⚠️ OpenAPI文档生成失败，继续构建"

# 构建
echo "🏗️ 开始构建..."
npm run build

echo "✅ 构建完成！"

# 显示构建结果
if [ -d "build" ]; then
    BUILD_SIZE=$(du -sh build | cut -f1)
    echo "📊 构建大小: $BUILD_SIZE"
    echo "📁 构建目录: $(pwd)/build"
else
    echo "❌ 构建失败"
    exit 1
fi