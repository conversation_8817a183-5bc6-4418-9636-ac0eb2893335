#!/bin/bash

# 镜像版本管理脚本
# 用于查找、验证和更新 Docker 镜像版本

set -e

# 颜色输出
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

log_info() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

log_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

log_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

log_warn() {
    echo -e "${YELLOW}[WARN]${NC} $1"
}

# 配置
REGISTRY="registry.qunhequnhe.com"
IMAGE_NAME="display/openapi-generator"
GITLAB_CI_FILE="backend-apis/.gitlab-ci.yml"

# 查询可用的镜像标签
list_available_tags() {
    local registry="$1"
    local image="$2"
    
    log_info "查询镜像标签: $registry/$image"
    
    # 尝试使用 API 查询
    local api_url="https://$registry/v2/$image/tags/list"
    
    if curl -s --fail "$api_url" 2>/dev/null; then
        log_success "成功获取标签列表"
    else
        log_warn "无法通过 API 获取标签，可能需要认证"
        log_info "请尝试手动查询: curl -u username:password $api_url"
        return 1
    fi
}

# 检查特定标签是否存在
check_tag_exists() {
    local registry="$1"
    local image="$2"
    local tag="$3"
    
    log_info "检查标签是否存在: $registry/$image:$tag"
    
    # 使用 docker manifest inspect 检查
    if docker manifest inspect "$registry/$image:$tag" >/dev/null 2>&1; then
        log_success "标签存在: $tag"
        return 0
    else
        log_error "标签不存在: $tag"
        return 1
    fi
}

# 比较镜像大小
compare_image_sizes() {
    local registry="$1"
    local image="$2"
    local tag1="$3"
    local tag2="$4"
    
    log_info "比较镜像大小: $tag1 vs $tag2"
    
    # 获取镜像信息
    local size1=$(docker manifest inspect "$registry/$image:$tag1" 2>/dev/null | jq -r '.config.size // 0')
    local size2=$(docker manifest inspect "$registry/$image:$tag2" 2>/dev/null | jq -r '.config.size // 0')
    
    if [ "$size1" != "null" ] && [ "$size2" != "null" ]; then
        log_info "$tag1 大小: $(numfmt --to=iec $size1)"
        log_info "$tag2 大小: $(numfmt --to=iec $size2)"
        
        if [ "$size1" -lt "$size2" ]; then
            log_success "$tag1 更小，推荐使用"
        elif [ "$size1" -gt "$size2" ]; then
            log_success "$tag2 更小，推荐使用"
        else
            log_info "两个镜像大小相同"
        fi
    else
        log_warn "无法获取镜像大小信息"
    fi
}

# 更新 GitLab CI 配置中的版本
update_version_in_config() {
    local new_version="$1"
    
    if [ ! -f "$GITLAB_CI_FILE" ]; then
        log_error "GitLab CI 配置文件不存在: $GITLAB_CI_FILE"
        return 1
    fi
    
    log_info "更新 GitLab CI 配置中的镜像版本为: $new_version"
    
    # 备份原文件
    cp "$GITLAB_CI_FILE" "${GITLAB_CI_FILE}.bak"
    
    # 更新版本
    if sed -i "s/OPENAPI_GENERATOR_VERSION: \".*\"/OPENAPI_GENERATOR_VERSION: \"$new_version\"/" "$GITLAB_CI_FILE"; then
        log_success "版本更新完成"
        log_info "原文件已备份为: ${GITLAB_CI_FILE}.bak"
        
        # 显示更改
        log_info "更改内容:"
        grep "OPENAPI_GENERATOR_VERSION" "$GITLAB_CI_FILE" || true
    else
        log_error "版本更新失败"
        # 恢复备份
        mv "${GITLAB_CI_FILE}.bak" "$GITLAB_CI_FILE"
        return 1
    fi
}

# 推荐版本策略
recommend_version_strategy() {
    log_info "=== 镜像版本策略推荐 ==="
    
    echo "1. 🎯 使用固定版本标签 (推荐)"
    echo "   - 优点: 稳定、可预测、缓存友好"
    echo "   - 示例: 1.0.0, 2.1.3, stable"
    
    echo ""
    echo "2. ⚠️  避免使用 latest 标签"
    echo "   - 缺点: 频繁更新、缓存失效、拉取超时"
    echo "   - 只在开发环境使用"
    
    echo ""
    echo "3. 📋 版本管理最佳实践:"
    echo "   - 定期更新到稳定版本"
    echo "   - 在测试环境验证新版本"
    echo "   - 记录版本更改日志"
    
    echo ""
    echo "4. 🚀 性能优化:"
    echo "   - 选择较小的镜像版本"
    echo "   - 使用多阶段构建"
    echo "   - 启用镜像层缓存"
}

# 主函数
main() {
    local command="${1:-help}"
    
    case "$command" in
        "list")
            list_available_tags "$REGISTRY" "$IMAGE_NAME"
            ;;
        "check")
            local tag="${2:-latest}"
            check_tag_exists "$REGISTRY" "$IMAGE_NAME" "$tag"
            ;;
        "compare")
            local tag1="${2:-latest}"
            local tag2="${3:-1.0.0}"
            compare_image_sizes "$REGISTRY" "$IMAGE_NAME" "$tag1" "$tag2"
            ;;
        "update")
            local version="${2}"
            if [ -z "$version" ]; then
                log_error "请指定要更新的版本"
                echo "用法: $0 update <version>"
                exit 1
            fi
            update_version_in_config "$version"
            ;;
        "recommend")
            recommend_version_strategy
            ;;
        "help"|*)
            echo "镜像版本管理工具"
            echo ""
            echo "用法: $0 <command> [options]"
            echo ""
            echo "命令:"
            echo "  list                    列出可用的镜像标签"
            echo "  check <tag>             检查指定标签是否存在"
            echo "  compare <tag1> <tag2>   比较两个标签的镜像大小"
            echo "  update <version>        更新 GitLab CI 配置中的版本"
            echo "  recommend               显示版本策略推荐"
            echo "  help                    显示此帮助信息"
            echo ""
            echo "示例:"
            echo "  $0 list"
            echo "  $0 check 1.0.0"
            echo "  $0 compare latest 1.0.0"
            echo "  $0 update 1.0.0"
            echo "  $0 recommend"
            ;;
    esac
}

# 如果脚本被直接执行
if [ "${BASH_SOURCE[0]}" == "${0}" ]; then
    main "$@"
fi
