---
openapi: "3.1.0"
info:
  title: "门窗设计管理API"
  description: "门窗设计服务REST API\n\n为群核旗下设计工具提供门窗模型的查询和批量更新操作。支持简单门窗（单一门窗单元）和复杂门窗（组合\
    型门窗，如L型、U型等复杂结构）的完整生命周期管理。提供高效的批量操作和异步处理机制，确保大规模门窗数据处理的性能和稳定性。\n\n**核心功能：**\n\
    - 门窗文档查询：获取指定楼层的完整门窗信息\n- 批量更新操作：支持多种门窗操作类型的批量处理\n- 多种门窗类型：简单门、简单窗、复杂门窗组合\n- 多\
    视图支持：BASIC、ADVANCED、FULL等不同详细程度的数据视图\n- 异步处理：批量操作采用异步机制，保证系统响应性能\n- 操作类型丰富：replace（\
    替换）、attach（附加）、detach（分离）、update（更新）\n\n**门窗类型支持：**\n- 简单门窗：单一门窗单元，如普通门、普通窗\n\
    - 复杂门窗：组合型门窗，如L型窗、U型窗、转角门等\n- 门类型：单开门、双开门、推拉门、折叠门等\n- 窗类型：平开窗、推拉窗、百叶窗、落地窗等\n\n\
    **业务应用场景：**\n- 建筑设计软件的门窗配置\n- 门窗产品的三维建模和展示\n- 建筑开口与门窗的关联管理\n- 门窗规格和参数的批量调整\n-\
    \ 复杂门窗组合的快速创建\n- 门窗数据的批量导入和同步\n\n**技术规范：**\n- 响应格式：JSON\n- 字符编码：UTF-8\n- 认证方式：\
    基于设计方案的权限验证\n- 批量限制：单次批量操作最多20个门窗\n- 异步支持：长时间批量操作的异步处理机制"
  contact:
    name: "群核科技开发团队"
    url: "https://wiki.manycore.com/doorwindow-design"
    email: "<EMAIL>"
  license:
    name: "群核科技专有许可证"
    url: "https://manycore.com/license"
  version: "v1.0.0"
servers:
- url: "http://localhost:8080"
  description: "本地开发环境"
- url: "https://api-dev.qunhe.com"
  description: "开发测试环境"
- url: "https://api.qunhe.com"
  description: "生产环境"
tags:
- name: "门窗管理 API"
  description: "门窗设计服务的 REST API，提供门窗模型的查询和批量更新操作。支持简单门窗（单一门窗单元）和复杂门窗（组合型门窗，如L型、U型\
    等复杂结构）的完整生命周期管理。"
paths:
  /dw/openapi/v1/rest/designs/{obsDesignId}/levels/{levelId}/document:batchupdate:
    post:
      tags:
      - "门窗管理 API"
      summary: "批量更新门窗"
      description: "对指定设计和楼层下的门窗进行批量更新操作。支持replace（替换）、sdAttach/swAttach（简单门/窗附加）、\
        detach（分离）、sdUpdate/swUpdate（简单门/窗更新）等多种操作类型。单次操作最多支持20个门窗，采用异步处理确保大批量操作的性\
        能。"
      operationId: "batchUpdate"
      parameters:
      - name: "obsDesignId"
        in: "path"
        description: "设计方案的唯一标识符"
        required: true
        schema:
          type: "integer"
          format: "int64"
        example: "3FO3N47AJ4FR"
      - name: "levelId"
        in: "path"
        description: "楼层的唯一标识符，用于标识设计方案中的具体楼层"
        required: true
        schema:
          type: "string"
        example: "M7AAU7AKTLKBCAABAAAAAAI8"
      requestBody:
        content:
          application/json:
            schema:
              $ref: "#/components/schemas/DoorWindowBatchUpdateRequest"
        required: true
      responses:
        500:
          description: "服务器内部错误，批量操作处理失败或系统异常"
          content:
            application/json:
              schema:
                $ref: "../../common/restapi-sdk-data-schemas.yaml#/components/schemas/ApiError"
        400:
          description: "请求参数错误，如批量大小超过限制、操作类型不正确、必需字段缺失等"
          content:
            application/json:
              schema:
                $ref: "../../common/restapi-sdk-data-schemas.yaml#/components/schemas/ApiError"
        404:
          description: "资源未找到，指定的设计方案或楼层不存在，或门窗ID不存在"
          content:
            application/json:
              schema:
                $ref: "../../common/restapi-sdk-data-schemas.yaml#/components/schemas/ApiError"
        200:
          description: "批量更新操作已成功提交，返回异步操作信息"
          content:
            application/json:
              schema:
                $ref: "../../common/restapi-sdk-data-schemas.yaml#/components/schemas/Operation"
              examples:
                批量更新响应示例:
                  description: "批量更新响应示例"
                  value:
                    operationId: "batch_update_20241201_001"
                    done: true
                    result:
                      elements:
                      - id: "door_001"
                        productId: "prod_door_single_001"
                        position:
                          x: 1.0
                          y: 0.0
                          z: 2.0
                        size:
                          x: 0.8
                          y: 0.1
                          z: 2.1
                        elementType: "SIMPLE_DOOR"
                        doorType: "SINGLE_DOOR"
                        moduleType: "MODEL"
  /dw/openapi/v1/rest/designs/{obsDesignId}/levels/{levelId}/document:
    get:
      tags:
      - "门窗管理 API"
      summary: "获取门窗文档"
      description: "根据方案ID和楼层ID获取门窗文档信息。返回指定楼层下的所有门窗数据，包括简单门窗（单一门窗单元）和复杂门窗（组合型门窗）。\
        支持不同数据视图以控制返回数据的详细程度，优化查询性能。"
      operationId: "getDoc"
      parameters:
      - name: "obsDesignId"
        in: "path"
        description: "设计方案的唯一标识符"
        required: true
        schema:
          type: "integer"
          format: "int64"
        example: "3FO3N47AJ4FR"
      - name: "levelId"
        in: "path"
        description: "楼层的唯一标识符，用于标识设计方案中的具体楼层"
        required: true
        schema:
          type: "string"
        example: "M7AAU7AKTLKBCAABAAAAAAI8"
      - name: "view"
        in: "query"
        description: "数据视图类型，控制返回数据的详细程度。BASIC返回基础信息，ADVANCED返回详细信息，FULL返回完整信息包括所有\
          关联数据"
        required: false
        schema:
          type: "string"
          enum:
          - "DOOR_WINDOW_DOCUMENT_VIEW"
          - "BASIC"
          - "ADVANCED"
          - "FULL"
          default: "BASIC"
        example: "BASIC"
      responses:
        200:
          description: "成功获取门窗文档"
          content:
            application/json:
              schema:
                $ref: "#/components/schemas/RestApiDoorWindowDocument"
              examples:
                门窗文档响应示例:
                  description: "门窗文档响应示例"
                  value:
                    id: "door_window_doc_001"
                    simpleDoors:
                    - id: "simple_door_001"
                      productId: "prod_door_001"
                      position:
                        x: 1.0
                        y: 0.0
                        z: 2.0
                      size:
                        x: 0.8
                        y: 0.1
                        z: 2.1
                      doorType: "SINGLE_DOOR"
                      openSide: "LEFT"
                      moduleType: "MODEL"
                    simpleWindows:
                    - id: "simple_window_001"
                      productId: "prod_window_001"
                      position:
                        x: 3.0
                        y: 0.0
                        z: 1.2
                      size:
                        x: 1.5
                        y: 0.2
                        z: 1.2
                      windowType: "WINDOW"
                      moduleType: "MODEL"
                    complexDoors: []
                    complexWindows: []
        404:
          description: "资源未找到，指定的设计方案或楼层不存在，或用户无权限访问"
          content:
            application/json:
              schema:
                $ref: "../../common/restapi-sdk-data-schemas.yaml#/components/schemas/ApiError"
        500:
          description: "服务器内部错误，数据库连接失败或系统异常"
          content:
            application/json:
              schema:
                $ref: "../../common/restapi-sdk-data-schemas.yaml#/components/schemas/ApiError"
        400:
          description: "请求参数错误，如方案ID格式不正确、楼层ID无效等"
          content:
            application/json:
              schema:
                $ref: "../../common/restapi-sdk-data-schemas.yaml#/components/schemas/ApiError"
components:
  schemas:
    DoorWindowBatchUpdateRequest:
      type: "object"
      properties:
        requestId:
          type: "string"
        operationId:
          type: "string"
        batchRequests:
          type: "array"
          items:
            oneOf:
            - $ref: "#/components/schemas/DoorWindowDetachRequest"
            - $ref: "#/components/schemas/DoorWindowReplaceRequest"
            - $ref: "#/components/schemas/SimpleDoorAttachRequest"
            - $ref: "#/components/schemas/SimpleDoorCreateRequest"
            - $ref: "#/components/schemas/SimpleDoorUpdateRequest"
            - $ref: "#/components/schemas/SimpleWindowAttachRequest"
            - $ref: "#/components/schemas/SimpleWindowCreateRequest"
            - $ref: "#/components/schemas/SimpleWindowUpdateRequest"
      description: "批量更新请求对象，包含待操作的门窗列表和操作配置。单次最多支持20个门窗的批量操作。"
    DoorWindowDetachRequest:
      required:
      - "id"
      - "type"
      type: "object"
      description: "门窗分离请求，用于将门窗从设计方案中分离"
      example:
        id: "door_123456"
        type: "MODEL"
        designId: "design_789"
        writeControl:
          versionCheck: true
          version: 1
      allOf:
      - $ref: "#/components/schemas/DoorWindowUpdateRequest"
      - type: "object"
        properties:
          id:
            type: "string"
            description: "要分离的门窗唯一标识符"
            example: "door_123456"
          type:
            type: "string"
            description: "门窗模块类型枚举，用于区分门窗的来源和特性"
            example: "MODEL"
            enum:
            - "MODULE_TYPE_UNSPECIFIED"
            - "MODEL"
            - "CUSTOM"
            - "CUSTOM_COPY"
    DoorWindowReplaceRequest:
      required:
      - "id"
      - "productId"
      - "type"
      type: "object"
      description: "门窗替换请求，用于将现有门窗替换为新产品"
      example:
        id: "door_123456"
        productId: "new_product_789"
        type: "MODEL"
        designId: "design_789"
        writeControl:
          versionCheck: true
          version: 1
      allOf:
      - $ref: "#/components/schemas/DoorWindowUpdateRequest"
      - type: "object"
        properties:
          id:
            type: "string"
            description: "要替换的门窗唯一标识符"
            example: "door_123456"
          productId:
            type: "string"
            description: "新产品的唯一标识符，支持OBS存储的产品ID"
            example: "new_product_789"
          type:
            type: "string"
            description: "门窗模块类型枚举，用于区分门窗的来源和特性"
            example: "MODEL"
            enum:
            - "MODULE_TYPE_UNSPECIFIED"
            - "MODEL"
            - "CUSTOM"
            - "CUSTOM_COPY"
    DoorWindowUpdateRequest:
      required:
      - "opType"
      type: "object"
      properties:
        requestId:
          type: "string"
        changeOpening:
          type: "boolean"
          description: "是否联动修改门窗关联的户型门洞。true表示修改门窗时同时更新关联的开口信息，false表示只修改门窗本身。"
          example: true
          default: true
        opType:
          type: "string"
      description: "门窗更新请求基类，定义所有门窗更新操作的通用请求结构"
      discriminator:
        propertyName: "opType"
    RestApiOpeningRelation:
      required:
      - "hostId"
      - "openingId"
      type: "object"
      properties:
        hostId:
          maxLength: 64
          type: "string"
          description: "关联的主体ID，通常是墙体ID。简单门窗只能关联单个墙体，适用于标准的门窗安装场景。"
          example: "wall_001"
        openingId:
          maxLength: 64
          type: "string"
          description: "开口ID，标识该门窗关联的具体开口位置。开口定义了门窗在墙体上的洞口信息，包括位置、尺寸等。"
          example: "opening_001"
      description: "简单门窗开口关联关系，定义简单门窗与单个墙体开口之间的一对一关联关系"
      example:
        hostId: "wall_001"
        openingId: "opening_001"
    SimpleDoorAttachRequest:
      required:
      - "openingRelation"
      - "productId"
      type: "object"
      description: "简单门附加请求，用于将门产品附加到墙体开口"
      example:
        productId: "door_product_456"
        openingRelation:
          hostId: "wall_123"
          openingId: "opening_456"
        openDirection:
          x: 1.0
          y: 0.0
          z: 0.0
        openSide: "LEFT"
        designId: "design_789"
        index: 1
      allOf:
      - $ref: "#/components/schemas/DoorWindowUpdateRequest"
      - type: "object"
        properties:
          productId:
            maxLength: 64
            type: "string"
            description: "产品ID，指定要附加的门窗产品。产品ID来自产品库，包含门窗的详细规格和属性信息。"
            example: "product_door_001"
          openingRelation:
            $ref: "#/components/schemas/RestApiOpeningRelation"
          openDirection:
            $ref: "../../common/geom-data-exchange-schemas.yaml#/components/schemas/Vector3d"
          openSide:
            type: "string"
            description: "门开启方向枚举，定义门扇的开启方向和旋转规则"
            example: "LEFT"
            enum:
            - "OPEN_SIDE_UNSPECIFIED"
            - "LEFT"
            - "RIGHT"
            - "UP"
            - "DOWN"
    SimpleDoorCreateRequest:
      required:
      - "openingRelation"
      - "productId"
      type: "object"
      description: "简单门创建请求，用于创建单开口关联的标准门型"
      example:
        productId: "door_product_123"
        openingRelation:
          hostId: "wall_456"
          openingId: "opening_789"
        openDirection:
          x: 1.0
          y: 0.0
          z: 0.0
        position:
          x: 100.0
          y: 200.0
          z: 0.0
        size:
          x: 800.0
          y: 2000.0
          z: 50.0
        openSide: "LEFT"
        designId: "design_123"
      allOf:
      - $ref: "#/components/schemas/DoorWindowUpdateRequest"
      - type: "object"
        properties:
          productId:
            type: "string"
            description: "门窗产品的唯一标识符，支持OBS存储的产品ID"
            example: "product_123456"
          openingRelation:
            $ref: "#/components/schemas/RestApiOpeningRelation"
          openDirection:
            $ref: "../../common/geom-data-exchange-schemas.yaml#/components/schemas/Vector3d"
          position:
            $ref: "../../common/geom-data-exchange-schemas.yaml#/components/schemas/Point3d"
          size:
            $ref: "../../common/geom-data-exchange-schemas.yaml#/components/schemas/Point3d"
          openSide:
            type: "string"
            description: "门开启方向枚举，定义门扇的开启方向和旋转规则"
            example: "LEFT"
            enum:
            - "OPEN_SIDE_UNSPECIFIED"
            - "LEFT"
            - "RIGHT"
            - "UP"
            - "DOWN"
    SimpleDoorUpdateRequest:
      required:
      - "id"
      - "type"
      type: "object"
      description: "简单门更新请求，用于更新已存在的简单门的属性"
      example:
        id: "door_123"
        type: "MODEL"
        openDirection:
          x: 1.0
          y: 0.0
          z: 0.0
        position:
          x: 150.0
          y: 250.0
          z: 0.0
        size:
          x: 900.0
          y: 2100.0
          z: 60.0
        openSide: "RIGHT"
        designId: "design_456"
      allOf:
      - $ref: "#/components/schemas/DoorWindowUpdateRequest"
      - type: "object"
        properties:
          id:
            maxLength: 64
            type: "string"
            description: "门窗ID，用于标识要更新的门窗对象"
            example: "door_001"
          type:
            type: "string"
            description: "门窗模块类型枚举，用于区分门窗的来源和特性"
            example: "MODEL"
            enum:
            - "MODULE_TYPE_UNSPECIFIED"
            - "MODEL"
            - "CUSTOM"
            - "CUSTOM_COPY"
          openDirection:
            $ref: "../../common/geom-data-exchange-schemas.yaml#/components/schemas/Vector3d"
          position:
            $ref: "../../common/geom-data-exchange-schemas.yaml#/components/schemas/Point3d"
          size:
            $ref: "../../common/geom-data-exchange-schemas.yaml#/components/schemas/Point3d"
          openSide:
            type: "string"
            description: "门开启方向枚举，定义门扇的开启方向和旋转规则"
            example: "RIGHT"
            enum:
            - "OPEN_SIDE_UNSPECIFIED"
            - "LEFT"
            - "RIGHT"
            - "UP"
            - "DOWN"
    SimpleWindowAttachRequest:
      required:
      - "openingRelation"
      - "productId"
      type: "object"
      description: "简单窗附加请求，用于将窗产品附加到墙体开口"
      example:
        productId: "window_product_456"
        openingRelation:
          hostId: "wall_123"
          openingId: "opening_456"
        openDirection:
          x: 1.0
          y: 0.0
          z: 0.0
        openSide: "LEFT"
        designId: "design_789"
        index: 1
      allOf:
      - $ref: "#/components/schemas/DoorWindowUpdateRequest"
      - type: "object"
        properties:
          productId:
            maxLength: 64
            type: "string"
            description: "产品ID，指定要附加的门窗产品。产品ID来自产品库，包含门窗的详细规格和属性信息。"
            example: "product_door_001"
          openingRelation:
            $ref: "#/components/schemas/RestApiOpeningRelation"
          openDirection:
            $ref: "../../common/geom-data-exchange-schemas.yaml#/components/schemas/Vector3d"
          openSide:
            type: "string"
            description: "窗开启方向枚举，定义窗扇的开启方向和旋转规则"
            example: "LEFT"
            enum:
            - "OPEN_SIDE_UNSPECIFIED"
            - "LEFT"
            - "RIGHT"
            - "UP"
            - "DOWN"
    SimpleWindowCreateRequest:
      required:
      - "openingRelation"
      - "productId"
      type: "object"
      description: "简单窗创建请求，用于创建单开口关联的标准窗型"
      example:
        productId: "window_product_123"
        openingRelation:
          hostId: "wall_456"
          openingId: "opening_789"
        openDirection:
          x: 1.0
          y: 0.0
          z: 0.0
        position:
          x: 100.0
          y: 200.0
          z: 50.0
        size:
          x: 1200.0
          y: 1500.0
          z: 20.0
        openSide: "LEFT"
        designId: "design_123"
      allOf:
      - $ref: "#/components/schemas/DoorWindowUpdateRequest"
      - type: "object"
        properties:
          productId:
            type: "string"
            description: "门窗产品的唯一标识符，支持OBS存储的产品ID"
            example: "product_123456"
          openingRelation:
            $ref: "#/components/schemas/RestApiOpeningRelation"
          openDirection:
            $ref: "../../common/geom-data-exchange-schemas.yaml#/components/schemas/Vector3d"
          position:
            $ref: "../../common/geom-data-exchange-schemas.yaml#/components/schemas/Point3d"
          size:
            $ref: "../../common/geom-data-exchange-schemas.yaml#/components/schemas/Point3d"
          openSide:
            type: "string"
            description: "窗开启方向枚举，定义窗扇的开启方向和旋转规则"
            example: "LEFT"
            enum:
            - "OPEN_SIDE_UNSPECIFIED"
            - "LEFT"
            - "RIGHT"
            - "UP"
            - "DOWN"
    SimpleWindowUpdateRequest:
      required:
      - "id"
      - "type"
      type: "object"
      description: "简单窗更新请求，用于更新已存在的简单窗的属性"
      example:
        id: "window_123"
        type: "MODEL"
        openDirection:
          x: 1.0
          y: 0.0
          z: 0.0
        position:
          x: 200.0
          y: 100.0
          z: 50.0
        size:
          x: 1500.0
          y: 1200.0
          z: 25.0
        openSide: "RIGHT"
        designId: "design_456"
      allOf:
      - $ref: "#/components/schemas/DoorWindowUpdateRequest"
      - type: "object"
        properties:
          id:
            maxLength: 64
            type: "string"
            description: "门窗ID，用于标识要更新的门窗对象"
            example: "door_001"
          type:
            type: "string"
            description: "门窗模块类型枚举，用于区分门窗的来源和特性"
            example: "MODEL"
            enum:
            - "MODULE_TYPE_UNSPECIFIED"
            - "MODEL"
            - "CUSTOM"
            - "CUSTOM_COPY"
          openDirection:
            $ref: "../../common/geom-data-exchange-schemas.yaml#/components/schemas/Vector3d"
          position:
            $ref: "../../common/geom-data-exchange-schemas.yaml#/components/schemas/Point3d"
          size:
            $ref: "../../common/geom-data-exchange-schemas.yaml#/components/schemas/Point3d"
          openSide:
            type: "string"
            description: "窗开启方向枚举，定义窗扇的开启方向和旋转规则"
            example: "RIGHT"
            enum:
            - "OPEN_SIDE_UNSPECIFIED"
            - "LEFT"
            - "RIGHT"
            - "UP"
            - "DOWN"
    RestApiComplexDoor:
      required:
      - "id"
      type: "object"
      description: "复杂门数据模型，表示复杂组合型门结构，支持多开口组合门和自由绘制门等高级门类型"
      example:
      - id: "complex_door_001"
        doorType: "ALUMINUM_DOOR"
      allOf:
      - $ref: "#/components/schemas/RestApiComplexDoorWindow"
      - type: "object"
        properties:
          doorType:
            type: "string"
            description: "复杂门类型枚举，定义复杂门的具体类型和结构特征"
            example: "ALUMINUM_DOOR"
            enum:
            - "COMPLEX_DOOR_TYPE_UNSPECIFIED"
            - "ALUMINUM_DOOR"
    RestApiCommonDoorWindow:
      required:
      - "id"
      type: "object"
      properties:
        id:
          maxLength: 64
          type: "string"
          description: "门窗的唯一标识符"
          example: "dw_001"
        productId:
          maxLength: 64
          type: "string"
          description: "关联的产品ID，用于获取门窗的详细产品信息"
          example: "prod_door_single_001"
        position:
          $ref: "../../common/geom-data-exchange-schemas.yaml#/components/schemas/Point3d"
        size:
          $ref: "../../common/geom-data-exchange-schemas.yaml#/components/schemas/Point3d"
        moduleType:
          type: "string"
          description: "门窗模块类型枚举，用于区分门窗的来源和特性"
          example: "MODEL"
          enum:
          - "MODULE_TYPE_UNSPECIFIED"
          - "MODEL"
          - "CUSTOM"
          - "CUSTOM_COPY"
      description: "门窗数据模型基类，定义所有门窗类型的公共属性"
    RestApiComplexDoorWindow:
      required:
      - "id"
      type: "object"
      properties:
        openingRelations:
          type: "array"
          description: "开口关联关系列表，定义该复杂门窗与多个墙体开口的关联关系。每个关联关系包含主体ID列表和开口ID。"
          example:
          - hostIds:
            - "wall_001"
            - "wall_002"
            openingId: "opening_001"
          - hostIds:
            - "wall_002"
            openingId: "opening_002"
          items:
            $ref: "#/components/schemas/RestApiComplexOpeningRelation"
      description: "复杂门窗数据模型基类，定义支持多开口关联的复杂门窗结构"
      allOf:
      - $ref: "#/components/schemas/RestApiCommonDoorWindow"
    RestApiComplexOpeningRelation:
      type: "object"
      properties:
        hostIds:
          type: "array"
          description: "关联的主体ID列表，通常是墙体ID列表。复杂门窗可以关联多个墙体，适用于转角窗、L型窗等跨墙体的门窗。"
          example:
          - "wall_001"
          - "wall_002"
          items:
            type: "string"
            description: "关联的主体ID列表，通常是墙体ID列表。复杂门窗可以关联多个墙体，适用于转角窗、L型窗等跨墙体的门窗。"
            example: "[\"wall_001\",\"wall_002\"]"
        openingId:
          maxLength: 64
          type: "string"
          description: "开口ID，标识该门窗关联的具体开口位置。开口定义了门窗在墙体上的洞口信息。"
          example: "opening_corner_001"
      description: "复杂门窗开口关联关系，定义复杂门窗与多个墙体开口之间的关联关系"
      example:
      - hostIds:
        - "wall_001"
        - "wall_002"
        openingId: "opening_001"
      - hostIds:
        - "wall_002"
        openingId: "opening_002"
    RestApiComplexWindow:
      required:
      - "id"
      type: "object"
      description: "复杂窗数据模型，表示复杂组合型窗结构，支持L型窗、U型窗、转角窗等高级窗类型"
      example:
      - id: "complex_window_001"
        windowType: "L_WINDOW"
      allOf:
      - $ref: "#/components/schemas/RestApiComplexDoorWindow"
      - type: "object"
        properties:
          windowType:
            type: "string"
            description: "复杂窗类型枚举，定义复杂窗的具体类型和几何结构特征"
            example: "L_WINDOW"
            enum:
            - "COMPLEX_WINDOW_TYPE_UNSPECIFIED"
            - "L_WINDOW"
            - "L_BAY_WINDOW"
            - "U_WINDOW"
            - "U_BAY_WINDOW"
            - "CORNER_WINDOW"
            - "ALUMINUM_WINDOW"
    RestApiDoorWindowDocument:
      type: "object"
      properties:
        id:
          maxLength: 64
          type: "string"
          description: "门窗文档的唯一标识符"
          example: "dw_doc_20241201_001"
        simpleDoors:
          type: "array"
          description: "简单门列表，包含该楼层下所有的简单门信息。简单门指单一门窗单元，如单开门、双开门、移门等。"
          example:
          - id: "simple_door_001"
            doorType: "SINGLE_DOOR"
            openSide: "LEFT"
          items:
            $ref: "#/components/schemas/RestApiSimpleDoor"
        complexDoors:
          type: "array"
          description: "复杂门列表，包含该楼层下所有的复杂门信息。复杂门指组合型门结构，支持多开口组合，如铝合金自由绘制门等。"
          example:
          - id: "complex_door_001"
            doorType: "ALUMINUM_DOOR"
          items:
            $ref: "#/components/schemas/RestApiComplexDoor"
        simpleWindows:
          type: "array"
          description: "简单窗列表，包含该楼层下所有的简单窗信息。简单窗指单一窗户单元，如普通窗、飘窗、落地窗等。"
          example:
          - id: "simple_window_001"
            windowType: "WINDOW"
          items:
            $ref: "#/components/schemas/RestApiSimpleWindow"
        complexWindows:
          type: "array"
          description: "复杂窗列表，包含该楼层下所有的复杂窗信息。复杂窗指组合型窗结构，如L型窗、U型窗、转角窗等。"
          example:
          - id: "complex_window_001"
            windowType: "L_WINDOW"
          items:
            $ref: "#/components/schemas/RestApiComplexWindow"
      description: "门窗文档数据模型，包含指定楼层下的所有门窗信息"
    RestApiSimpleDoor:
      required:
      - "id"
      type: "object"
      description: "简单门数据模型，表示单一门窗单元的门类型，支持单开门、双开门、移门等常见门类型"
      example:
      - id: "simple_door_001"
        doorType: "SINGLE_DOOR"
        openSide: "LEFT"
      allOf:
      - $ref: "#/components/schemas/RestApiSimpleDoorWindow"
      - type: "object"
        properties:
          doorType:
            type: "string"
            description: "简单门类型枚举，定义简单门的具体类型和结构特征"
            example: "SINGLE_DOOR"
            enum:
            - "SIMPLE_DOOR_TYPE_UNSPECIFIED"
            - "SINGLE_DOOR"
            - "DOUBLE_DOOR"
            - "SLIDING_DOOR"
            - "COMPOSITE_DOOR"
            - "PASS"
    RestApiSimpleDoorWindow:
      required:
      - "id"
      type: "object"
      properties:
        openingRelation:
          $ref: "#/components/schemas/RestApiOpeningRelation"
        openDirection:
          $ref: "../../common/geom-data-exchange-schemas.yaml#/components/schemas/Vector3d"
      description: "简单门窗数据模型基类，定义与单个开口关联的标准门窗结构"
      allOf:
      - $ref: "#/components/schemas/RestApiCommonDoorWindow"
    RestApiSimpleWindow:
      required:
      - "id"
      type: "object"
      properties:
        version:
          maxLength: 32
          type: "string"
          description: "门窗模型的版本号，用于版本控制和兼容性管理"
          example: "1.0.0"
        windowType:
          type: "string"
          description: "简单窗类型枚举，定义简单窗的具体类型和结构特征"
          example: "SLIDING_WINDOW"
          enum:
          - "SIMPLE_WINDOW_TYPE_UNSPECIFIED"
          - "SLIDING_WINDOW"
          - "CASEMENT_WINDOW"
          - "FIXED_WINDOW"
          - "BAY_WINDOW"
      description: "简单窗数据模型，表示单一窗单元结构，支持推拉窗、平开窗等标准窗类型"
      allOf:
      - $ref: "#/components/schemas/RestApiSimpleDoorWindow"
