# API Registry 项目结构

本文档描述了重构后的统一 API 注册中心项目目录结构和组织方式。

## 📁 目录结构概览

```
api-registry/
├── frontend-apis/              # 前端 API（小程序）
│   ├── packages/              # 小程序 API 包
│   │   ├── idp-sdk/          # 公网前端 SDK
│   │   ├── idp-sdk-internal/ # 内网前端 SDK
│   │   ├── idp-app-core/     # 应用核心
│   │   ├── idp-common/       # 公共接口
│   │   ├── idp-math/         # 数学计算
│   │   ├── idp-decoration/   # 硬装相关
│   │   ├── idp-floorplan/    # 户型图相关
│   │   └── ...               # 其他小程序模块
│   ├── scripts/              # 前端构建脚本
│   ├── templates/            # 前端包模板
│   └── configs/              # 前端专用配置
├── backend-apis/               # 后端 API
│   ├── specifications/        # API 规范文件
│   │   ├── services/         # 各服务的 OpenAPI 规范
│   │   │   ├── camera/
│   │   │   │   └── openapi.yaml # 相机基础设施 API 规范
│   │   │   ├── doorwindow/
│   │   │   │   └── openapi.yaml # 门窗服务 API 规范
│   │   │   ├── furniture/
│   │   │   │   └── openapi.yaml # 家具设计 API 规范
│   │   │   ├── koolux/
│   │   │   │   └── openapi.yaml # Koolux 服务 API 规范
│   │   │   ├── layout/
│   │   │   │   └── openapi.yaml # 布局服务 API 规范
│   │   │   └── pdm/
│   │   │       └── openapi.yaml # PDM 服务 API 规范
│   │   ├── common/           # 通用数据模型和组件
│   │   └── shared/           # 共享数据映射
│   ├── generators/            # SDK 和文档生成器
│   │   ├── sdks/             # SDK 生成器
│   │   │   ├── configs/      # 生成器配置文件
│   │   │   │   ├── java/     # Java SDK 配置
│   │   │   │   │   ├── camera.yaml
│   │   │   │   │   ├── doorwindow.yaml
│   │   │   │   │   ├── furniture.yaml
│   │   │   │   │   ├── koolux.yaml
│   │   │   │   │   ├── layout.yaml
│   │   │   │   │   └── pdm.yaml
│   │   │   │   └── typescript/    # TypeScript SDK 配置
│   │   │   └── scripts/           # 生成脚本
│   │   └── docs/                  # 文档生成器
│   ├── configs/                   # 后端配置文件
│   ├── preview-docs/              # 📖 预览文档系统（API 评审预览）
│   │   ├── docs/                  # 预览文档内容 (Markdown)
│   │   ├── blog/                  # 更新日志博客
│   │   ├── src/                   # 自定义组件和页面
│   │   ├── guides/                # API 评审指南
│   │   ├── docusaurus.config.ts   # 支持动态配置
│   │   ├── sidebars.ts            # 动态生成的侧边栏
│   │   └── package.json           # 依赖配置
│   ├── temp_templates/            # 生成器模板
│   └── examples/                  # 示例代码
├── documentation/                 # 📖 统一对外文档系统
│   ├── guides/                    # 📚 开发指南
│   │   ├── getting-started/       # 快速开始指南
│   │   │   ├── frontend-setup.md # 前端环境搭建
│   │   │   ├── backend-setup.md  # 后端环境搭建
│   │   │   └── integration.md    # 前后端集成指南
│   │   ├── development/           # 开发指南
│   │   │   ├── frontend-dev.md   # 前端开发指南
│   │   │   ├── backend-dev.md    # 后端开发指南
│   │   │   ├── testing.md        # 测试指南
│   │   │   └── debugging.md      # 调试指南
│   │   ├── deployment/            # 部署指南
│   │   │   ├── frontend-deploy.md # 前端部署
│   │   │   ├── backend-deploy.md # 后端部署
│   │   │   └── ci-cd.md          # CI/CD 配置
│   │   └── best-practices/        # 最佳实践
│   │       ├── frontend-practices.md # 前端最佳实践
│   │       ├── backend-practices.md  # 后端最佳实践
│   │       ├── security.md       # 安全最佳实践
│   │       └── performance.md    # 性能优化
│   ├── api-reference/             # 📖 API 参考文档
│   │   ├── frontend/              # 前端 API 参考
│   │   │   ├── miniapp/          # 小程序 API
│   │   │   │   ├── core-apis.md  # 核心 API
│   │   │   │   ├── business-apis.md # 业务 API
│   │   │   │   └── tools-apis.md # 工具 API
│   │   │   └── sdk/              # 前端 SDK
│   │   │       ├── installation.md # 安装指南
│   │   │       ├── configuration.md # 配置说明
│   │   │       └── api-methods.md # API 方法
│   │   └── backend/               # 后端 API 参考
│   │       ├── rest-api/         # REST API
│   │       │   ├── authentication.md # 认证接口
│   │       │   ├── user-management.md # 用户管理
│   │       │   └── data-operations.md # 数据操作
│   │       └── sdk/              # 后端 SDK
│   │           ├── java-sdk.md   # Java SDK
│   │           ├── typescript-sdk.md # TypeScript SDK
│   │           └── integration.md # 集成指南
│   ├── examples/                  # 💡 代码示例
│   │   ├── frontend/              # 前端示例
│   │   │   ├── miniapp/          # 小程序示例
│   │   │   │   ├── basic-usage/  # 基础用法
│   │   │   │   ├── advanced-features/ # 高级功能
│   │   │   │   └── real-world/   # 实际案例
│   │   │   └── integration/      # 集成示例
│   │   │       ├── with-react/   # React 集成
│   │   │       └── with-vue/     # Vue 集成
│   │   └── backend/               # 后端示例
│   │       ├── api-usage/        # API 使用示例
│   │       │   ├── authentication/ # 认证示例
│   │       │   ├── data-crud/    # 数据 CRUD
│   │       │   └── file-upload/  # 文件上传
│   │       └── sdk-integration/  # SDK 集成示例
│   │           ├── java-examples/ # Java 示例
│   │           └── node-examples/ # Node.js 示例
│   ├── tutorials/                 # 🎓 分步教程
│   │   ├── frontend/              # 前端教程
│   │   │   ├── miniapp-development.md # 小程序开发教程
│   │   │   ├── ui-components.md  # UI 组件教程
│   │   │   └── state-management.md # 状态管理教程
│   │   ├── backend/               # 后端教程
│   │   │   ├── api-development.md # API 开发教程
│   │   │   ├── database-design.md # 数据库设计教程
│   │   │   └── microservices.md  # 微服务教程
│   │   └── integration/           # 集成教程
│   │       ├── frontend-backend.md # 前后端集成
│   │       ├── third-party.md    # 第三方集成
│   │       └── testing-strategies.md # 测试策略
│   ├── assets/                    # 🎨 静态资源
│   │   ├── images/                # 图片资源
│   │   │   ├── frontend/         # 前端相关图片
│   │   │   ├── backend/          # 后端相关图片
│   │   │   └── architecture/     # 架构图片
│   │   ├── diagrams/              # 图表
│   │   │   ├── sequence/         # 时序图
│   │   │   ├── architecture/     # 架构图
│   │   │   └── flow/             # 流程图
│   │   ├── icons/                 # 图标文件
│   │   ├── logos/                 # Logo 文件
│   │   └── screenshots/           # 截图文件
│   │       ├── frontend/         # 前端截图
│   │       └── backend/          # 后端截图
│   ├── changelog/                 # 📝 变更日志
│   │   ├── frontend/              # 前端变更日志
│   │   │   ├── CHANGELOG.md      # 前端总变更日志
│   │   │   └── versions/         # 版本详细日志
│   │   └── backend/               # 后端变更日志
│   │       ├── CHANGELOG.md      # 后端总变更日志
│   │       └── versions/         # 版本详细日志
│   └── specs/                     # 📋 规范文档
│       ├── frontend/              # 前端规范
│       │   ├── coding-standards.md # 编码规范
│       │   ├── api-design.md     # API 设计规范
│       │   └── testing-standards.md # 测试规范
│       └── backend/               # 后端规范
│           ├── api-standards.md  # API 标准
│           ├── data-models.md    # 数据模型规范
│           └── security-standards.md # 安全规范
│   │   │   ├── koolux.yaml
│   │   │   ├── layout.yaml
│   │   │   └── pdm.yaml
│   │   └── typescript/         # TypeScript SDK 配置（预留）
│   ├── templates/              # 生成器模板
│   │   ├── java/              # Java SDK 模板
│   │   │   └── libraries/
│   │   │       └── resttemplate/
│   │   ├── typescript/         # TypeScript 模板（预留）
│   │   └── docs/              # 文档生成模板
│   │       ├── api-preview.html
│   │       ├── no-api-changes.html
│   │       ├── preview-info.html
│   │       └── service-card.html
│   └── scripts/                # 生成器脚本
│       ├── generate-all-dynamic-content.sh
│       ├── generate-docs-content.js
│       ├── generate-openapi-config.js
│       ├── generate-mappings.sh
│       └── generate-restapi-sdk-data-mappings.sh
├── build/                      # 构建输出目录（被 .gitignore）
│   ├── sdks/                  # 生成的 SDK
│   │   ├── camera/
│   │   │   ├── java/
│   │   │   └── typescript/
│   │   ├── doorwindow/
│   │   │   ├── java/
│   │   │   └── typescript/
│   │   └── ...                # 其他服务
│   ├── docs/                  # 生成的文档
│   └── artifacts/             # 其他构建产物
├── documentation/              # 项目文档
│   ├── website/               # Docusaurus 文档站点
│   │   ├── docs/             # 文档内容
│   │   ├── blog/             # 博客文章
│   │   ├── src/              # 自定义组件
│   │   ├── static/           # 静态资源
│   │   ├── docusaurus.config.ts
│   │   ├── package.json
│   │   └── ...               # 其他配置文件
│   ├── guides/               # 开发指南
│   │   ├── api-review-process.md
│   │   └── gitlab-setup-guide.md
│   └── api-specs/            # API 规范文档（预留）
├── tools/                     # 开发和运维工具
│   ├── ci-cd/                # CI/CD 脚本
│   │   ├── ci/              # 原有 CI 子系统
│   │   │   ├── deployment/
│   │   │   ├── docs/
│   │   │   ├── review/
│   │   │   ├── setup/
│   │   │   └── validation/
│   │   ├── add-mr-comment.sh
│   │   ├── check-local-ci.sh
│   │   ├── detect-api-changes.sh
│   │   └── local_run.sh
│   ├── deployment/           # 部署脚本
│   │   └── deploy-docs-to-manual.sh
│   ├── development/          # 本地开发工具
│   │   ├── local/           # 本地开发子系统
│   │   ├── setup-docs.sh
│   │   ├── setup-local-env.sh
│   │   ├── setup-local-env-final.sh
│   │   ├── install-docs-tools.sh
│   │   ├── switch-npm-registry.sh
│   │   ├── pre-commit-hook.sh
│   │   └── check-project-status.sh
│   └── utilities/            # 通用工具
│       ├── utils/
│       │   ├── load-env-vars.sh
│       │   ├── process-file-list.sh
│       │   └── process-template.sh
│       └── README-mappings.md
├── config/                    # 项目配置文件
│   ├── ci-cd/                # CI/CD 相关配置
│   │   └── ci-settings.xml
│   ├── docker/               # Docker 相关配置
│   │   ├── Dockerfile
│   │   └── build-docker.sh
│   └── project/              # 项目级配置
│       └── code-stage.json
├── examples/                  # 示例代码（保持不变）
├── geom-data-exchange-java/   # 几何数据交换 Java 库（保持不变）
├── test-results/              # 测试结果（保持不变）
├── ui/                        # UI 组件（保持不变）
├── .gitignore                 # Git 忽略配置
├── .gitlab-ci.yml             # GitLab CI 配置
├── package.json               # Node.js 项目配置
├── package-lock.json          # 依赖锁定文件
├── README.md                  # 项目说明
└── PROJECT_STRUCTURE.md       # 本文档
```

## 🎯 重构目标和优势

### 1. **清晰的功能分离**
- **specifications/** - 纯粹的 API 规范管理，与生成配置分离
- **generators/** - 所有生成相关的工具、模板和配置集中管理
- **tools/** - 开发和运维工具按功能分类
- **documentation/** - 文档相关内容统一管理

### 2. **更好的可维护性**
- 相关文件集中存放，减少查找时间
- 统一的命名规范，降低认知负担
- 清晰的层级结构，便于新人理解

### 3. **增强的扩展性**
- 新增服务有明确的放置位置
- 新增语言支持结构清晰
- 工具脚本分类明确，便于扩展

## 🔄 主要变更

| 原路径 | 新路径 | 说明 |
|--------|--------|------|
| `openapi/` | `specifications/services/` | API 规范文件 |
| `output/` | `build/` | 构建输出目录 |
| `docs-site/` | `documentation/website/` | 文档站点 |
| `docs/` | `documentation/guides/` | 开发指南 |
| `scripts/` | `tools/` + `generators/scripts/` | 脚本工具分类 |
| `templates/` | `generators/templates/docs/` | 文档模板 |
| `generator/templates/` | `generators/templates/java/` | Java 模板 |
| `restapi.yaml` | `openapi.yaml` | API 规范文件名 |
| `config-java.yaml` | `generators/configs/java/*.yaml` | SDK 配置 |

## 📝 使用说明

### 添加新服务
1. 在 `specifications/services/new-service/` 创建 `openapi.yaml`
2. 在 `generators/configs/java/` 创建 `new-service.yaml`
3. 运行 `generators/docs/scripts/generate-all-dynamic-content.sh`

### 本地开发
- 环境设置：`tools/development/setup-local-env-final.sh`
- 文档预览：`tools/development/setup-docs.sh`
- 本地构建：`tools/development/local/`

### CI/CD
所有 CI/CD 相关脚本位于 `tools/ci-cd/` 目录下，GitLab CI 配置已更新路径引用。

## 🔧 配置更新

重构过程中已更新以下配置文件的路径引用：
- `.gitlab-ci.yml` - CI/CD 流程配置
- `documentation/website/openapi-config.json` - 文档站点配置
- `generators/docs/scripts/generate-all-dynamic-content.sh` - 生成器脚本
- 所有 SDK 生成配置文件中的路径引用

## ⚠️ 注意事项

1. **构建输出** - `build/` 目录在 `.gitignore` 中，不会被版本控制
2. **模板文件** - `temp_templates/` 是本地参考用的模板，已在 `.gitignore` 中
3. **路径引用** - 所有脚本和配置文件中的路径引用已更新
4. **向后兼容** - 此重构不保持向后兼容性，需要更新所有引用

---

*此文档描述了项目重构后的最终结构。如有疑问，请参考各目录下的 README 文件或联系开发团队。*
