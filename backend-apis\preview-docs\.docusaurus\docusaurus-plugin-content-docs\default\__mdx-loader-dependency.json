{"options": {"sidebarPath": "/mnt/c/workspace/backend-api-sdk/documentation/website/sidebars.ts", "docItemComponent": "@theme/ApiItem", "showLastUpdateTime": false, "showLastUpdateAuthor": false, "exclude": ["**/_*.{js,jsx,ts,tsx,md,mdx}", "**/_*/**", "**/*.test.{js,jsx,ts,tsx}", "**/__tests__/**"], "includeCurrentVersion": true, "breadcrumbs": false, "remarkPlugins": [], "rehypePlugins": [], "path": "docs", "editCurrentVersion": false, "editLocalizedFiles": false, "routeBasePath": "docs", "tagsBasePath": "tags", "include": ["**/*.{md,mdx}"], "sidebarCollapsible": true, "sidebarCollapsed": true, "docsRootComponent": "@theme/DocsRoot", "docVersionRootComponent": "@theme/DocVersionRoot", "docRootComponent": "@theme/DocRoot", "docTagsListComponent": "@theme/DocTagsListPage", "docTagDocListComponent": "@theme/DocTagDocListPage", "docCategoryGeneratedIndexComponent": "@theme/DocCategoryGeneratedIndexPage", "recmaPlugins": [], "beforeDefaultRemarkPlugins": [], "beforeDefaultRehypePlugins": [], "admonitions": true, "disableVersioning": false, "versions": {}, "onInlineTags": "warn", "id": "default"}, "versionsMetadata": [{"versionName": "current", "label": "Next", "banner": null, "badge": false, "noIndex": false, "className": "docs-version-current", "path": "/manycoreapi-demo/0.0.4/docs", "tagsPath": "/manycoreapi-demo/0.0.4/docs/tags", "isLast": true, "routePriority": -1, "sidebarFilePath": "/mnt/c/workspace/backend-api-sdk/documentation/website/sidebars.ts", "contentPath": "/mnt/c/workspace/backend-api-sdk/documentation/website/docs", "contentPathLocalized": "/mnt/c/workspace/backend-api-sdk/documentation/website/i18n/zh-Hans/docusaurus-plugin-content-docs/current"}]}