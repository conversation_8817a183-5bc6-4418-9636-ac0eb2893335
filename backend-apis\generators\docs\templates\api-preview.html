<!DOCTYPE html>
<html>
<head>
    <title>MR !${CI_MERGE_REQUEST_IID} - API文档预览</title>
    <meta charset="utf-8">
    <meta name="viewport" content="width=device-width, initial-scale=1">
    <style>
        body { 
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', 'Helvetica Neue', Arial, sans-serif;
            margin: 0; 
            padding: 40px; 
            background: #f8f9fa; 
            line-height: 1.6;
        }
        .container { 
            max-width: 1200px; 
            margin: 0 auto; 
        }
        .header { 
            background: white; 
            padding: 32px; 
            border-radius: 12px; 
            margin-bottom: 24px; 
            box-shadow: 0 4px 6px rgba(0,0,0,0.1);
            border-left: 4px solid #007bff;
        }
        .header h1 {
            margin: 0 0 16px 0;
            color: #2c3e50;
            font-size: 2em;
        }
        .header p {
            margin: 8px 0;
            color: #6c757d;
            font-size: 0.95em;
        }
        .header strong {
            color: #495057;
        }
        .services-section {
            margin-bottom: 24px;
        }
        .services-section h2 {
            color: #2c3e50;
            margin-bottom: 16px;
            font-size: 1.5em;
        }
        .service-card { 
            background: white; 
            border-radius: 12px; 
            padding: 24px; 
            margin: 16px 0; 
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
            border: 1px solid #e9ecef;
            transition: transform 0.2s, box-shadow 0.2s;
        }
        .service-card:hover {
            transform: translateY(-2px);
            box-shadow: 0 4px 12px rgba(0,0,0,0.15);
        }
        .service-card h3 {
            margin: 0 0 12px 0;
            color: #2c3e50;
            font-size: 1.3em;
        }
        .service-card p {
            margin: 0 0 16px 0;
            color: #6c757d;
        }
        .btn { 
            display: inline-block; 
            padding: 12px 24px; 
            background: #007bff; 
            color: white; 
            text-decoration: none; 
            border-radius: 8px; 
            font-weight: 500;
            transition: background 0.2s;
        }
        .btn:hover { 
            background: #0056b3; 
            text-decoration: none;
            color: white;
        }
        .checklist { 
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 32px; 
            border-radius: 12px; 
            margin-top: 32px;
        }
        .checklist h4 {
            margin: 0 0 16px 0;
            font-size: 1.3em;
            color: white;
        }
        .checklist ul {
            margin: 16px 0;
            padding-left: 20px;
        }
        .checklist li {
            margin: 8px 0;
            line-height: 1.6;
        }
        .checklist code {
            background: rgba(255,255,255,0.2);
            padding: 2px 6px;
            border-radius: 4px;
            font-size: 0.9em;
        }
        .no-services {
            text-align: center;
            padding: 40px;
            background: white;
            border-radius: 12px;
            color: #6c757d;
        }
        .service-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(320px, 1fr));
            gap: 20px;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>🔍 API 文档预览</h1>
            <p><strong>MR:</strong> !${CI_MERGE_REQUEST_IID}</p>
            <p><strong>分支:</strong> ${CI_COMMIT_REF_NAME}</p>
            <p><strong>提交:</strong> ${CI_COMMIT_SHORT_SHA}</p>
            <p><strong>生成时间:</strong> ${GENERATED_TIME}</p>
        </div>

        <div class="services-section">
            <h2>📋 服务文档</h2>
            <div class="service-grid">
                ${SERVICE_CARDS}
            </div>
        </div>

        <div class="checklist">
            <h4>📋 评审检查清单</h4>
            <ul>
                <li>API 设计符合 RESTful 原则</li>
                <li>新增/修改的端点文档完整</li>
                <li>错误响应格式符合标准</li>
                <li>安全性考虑充分</li>
                <li>向后兼容性评估完成</li>
            </ul>
            
            <h4>🏷️ 评审标签</h4>
            <p>请在MR中添加以下标签之一：</p>
            <ul>
                <li><code>API-Review::Approved</code> - 评审通过</li>
                <li><code>API-Review::Changes-Requested</code> - 需要修改</li>
                <li><code>API-Review::In-Progress</code> - 评审中</li>
            </ul>
        </div>
    </div>
</body>
</html> 