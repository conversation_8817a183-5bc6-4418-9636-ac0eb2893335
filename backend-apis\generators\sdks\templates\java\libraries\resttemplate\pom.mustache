<project xmlns="http://maven.apache.org/POM/4.0.0" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/maven-v4_0_0.xsd">
    <modelVersion>4.0.0</modelVersion>
    <groupId>{{groupId}}</groupId>
    <artifactId>{{artifactId}}</artifactId>
    <packaging>jar</packaging>
    <name>{{artifactId}}</name>
    <version>{{artifactVersion}}</version>
    <url>{{artifactUrl}}</url>
    <description>{{artifactDescription}}</description>

    <!-- === SCM Information === -->
    <scm>
        <connection>{{scmConnection}}</connection>
        <developerConnection>{{scmDeveloperConnection}}</developerConnection>
        <url>{{scmUrl}}</url>
        <tag>HEAD</tag>
    </scm>

{{#parentOverridden}}
    <parent>
        <groupId>{{{parentGroupId}}}</groupId>
        <artifactId>{{{parentArtifactId}}}</artifactId>
        <version>{{{parentVersion}}}</version>
    </parent>
{{/parentOverridden}}

    <!-- === License Information === -->
    <licenses>
        <license>
            <name>{{licenseName}}</name>
            <url>{{licenseUrl}}</url>
            <distribution>repo</distribution>
        </license>
    </licenses>

    <!-- === Developer Information === -->
    <developers>
        <developer>
            <name>{{developerName}}</name>
            <email>{{developerEmail}}</email>
            <organization>{{developerOrganization}}</organization>
            <organizationUrl>{{developerOrganizationUrl}}</organizationUrl>
        </developer>
    </developers>

    <!-- === Distribution Management (for internal releases and snapshots) === -->
    <distributionManagement>
        <repository>
            <id>qunhe-releases</id>
            <name>Qunhe Release Repository</name>
            <url>http://nexus.qunhequnhe.com/repository/maven-releases/</url>
        </repository>
        <snapshotRepository>
            <id>qunhe-snapshots</id>
            <name>Qunhe Snapshot Repository</name>
            <url>http://nexus.qunhequnhe.com/repository/maven-snapshots/</url>
        </snapshotRepository>
    </distributionManagement>

    <build>
        <plugins>
            <plugin>
                <groupId>org.apache.maven.plugins</groupId>
                <artifactId>maven-enforcer-plugin</artifactId>
                <version>3.4.0</version>
                <executions>
                    <execution>
                        <id>enforce-maven</id>
                        <goals>
                            <goal>enforce</goal>
                        </goals>
                        <configuration>
                            <rules>
                                <requireMavenVersion>
                                    <version>2.2.0</version>
                                </requireMavenVersion>
                            </rules>
                        </configuration>
                    </execution>
                </executions>
            </plugin>
            <plugin>
                <groupId>org.apache.maven.plugins</groupId>
                <artifactId>maven-surefire-plugin</artifactId>
                <version>3.1.2</version>
                <configuration>
                    <systemPropertyVariables>
                        <property>
                            <name>loggerPath</name>
                            <value>conf/log4j.properties</value>
                        </property>
                    </systemPropertyVariables>
                    <argLine>-Xms512m -Xmx1500m</argLine>
                    <parallel>methods</parallel>
                    <reuseForks>false</reuseForks>
                    <useUnlimitedThreads>true</useUnlimitedThreads>
                </configuration>
                <dependencies>
                    <!--Custom provider and engine for Junit 5 to surefire-->
                    <dependency>
                        <groupId>org.junit.jupiter</groupId>
                        <artifactId>junit-jupiter-engine</artifactId>
                        <version>${junit-version}</version>
                    </dependency>
                </dependencies>
            </plugin>
            <plugin>
                <artifactId>maven-dependency-plugin</artifactId>
                <executions>
                    <execution>
                        <phase>package</phase>
                        <goals>
                            <goal>copy-dependencies</goal>
                        </goals>
                        <configuration>
                            <outputDirectory>${project.build.directory}/lib</outputDirectory>
                        </configuration>
                    </execution>
                </executions>
            </plugin>

            <!-- attach test jar -->
            <plugin>
                <groupId>org.apache.maven.plugins</groupId>
                <artifactId>maven-jar-plugin</artifactId>
                <version>3.3.0</version>
                <executions>
                    <execution>
                        <goals>
                            <goal>test-jar</goal>
                        </goals>
                    </execution>
                </executions>
                <configuration>
                </configuration>
            </plugin>

            <plugin>
                <groupId>org.codehaus.mojo</groupId>
                <artifactId>build-helper-maven-plugin</artifactId>
                <version>3.4.0</version>
                <executions>
                    <execution>
                        <id>add_sources</id>
                        <phase>generate-sources</phase>
                        <goals>
                            <goal>add-source</goal>
                        </goals>
                        <configuration>
                            <sources>
                                <source>src/main/java</source>
                            </sources>
                        </configuration>
                    </execution>
                    <execution>
                        <id>add_test_sources</id>
                        <phase>generate-test-sources</phase>
                        <goals>
                            <goal>add-test-source</goal>
                        </goals>
                        <configuration>
                            <sources>
                                <source>src/test/java</source>
                            </sources>
                        </configuration>
                    </execution>
                </executions>
            </plugin>
            <plugin>
                <groupId>org.apache.maven.plugins</groupId>
                <artifactId>maven-compiler-plugin</artifactId>
                <version>3.11.0</version>
                <configuration>
                {{#useJakartaEe}}
                    <source>17</source>
                    <target>17</target>
                {{/useJakartaEe}}
                {{^useJakartaEe}}
                    <source>1.8</source>
                    <target>1.8</target>
                {{/useJakartaEe}}
                </configuration>
            </plugin>
            
            <!-- Source Plugin (Required by Central) -->
            <plugin>
                <groupId>org.apache.maven.plugins</groupId>
                <artifactId>maven-source-plugin</artifactId>
                <version>3.3.0</version>
                <executions>
                    <execution>
                        <id>attach-sources</id>
                        <goals>
                            <goal>jar-no-fork</goal>
                        </goals>
                    </execution>
                </executions>
            </plugin>
            
            <!-- Javadoc Plugin -->
            <plugin>
                <groupId>org.apache.maven.plugins</groupId>
                <artifactId>maven-javadoc-plugin</artifactId>
                <version>3.5.0</version>
                <configuration>
                    <doclint>none</doclint>
                    <show>private</show>
                    <encoding>UTF-8</encoding>
                {{#useJakartaEe}}
                    <source>17</source>
                {{/useJakartaEe}}
                {{^useJakartaEe}}
                    <source>1.8</source>
                {{/useJakartaEe}}
                </configuration>
                <executions>
                    <execution>
                        <id>attach-javadocs</id>
                        <phase>package</phase>
                        <goals>
                            <goal>jar</goal>
                        </goals>
                    </execution>
                </executions>
            </plugin>
            
            <!-- Release Plugin -->
            <plugin>
                <groupId>org.apache.maven.plugins</groupId>
                <artifactId>maven-release-plugin</artifactId>
                <version>2.5.2</version>
                <configuration>
                    <tagNameFormat>@{project.version}</tagNameFormat>
                    <autoVersionSubmodules>true</autoVersionSubmodules>
                </configuration>
            </plugin>
        </plugins>
    </build>

    <profiles>
        <!-- Profile for Maven Central release -->
        <profile>
            <id>central-release</id>
            <build>
                <plugins>
                    <!-- Central Publishing Plugin (Handles deployment to Central Portal) -->
                    <plugin>
                        <groupId>org.sonatype.central</groupId>
                        <artifactId>central-publishing-maven-plugin</artifactId>
                        <version>0.7.0</version>
                        <extensions>true</extensions>
                        <configuration>
                            <publishingServerId>central</publishingServerId>
                            <!-- Auto-publish after successful upload and validation -->
                            <autoPublish>true</autoPublish>
                            <!-- Check deployment status every 10 seconds -->
                            <waitUntil>published</waitUntil>
                        </configuration>
                    </plugin>
                    
                    <!-- GPG Plugin (Signs artifacts) -->
                    <plugin>
                        <groupId>org.apache.maven.plugins</groupId>
                        <artifactId>maven-gpg-plugin</artifactId>
                        <version>3.1.0</version>
                        <executions>
                            <execution>
                                <id>sign-artifacts</id>
                                <phase>verify</phase>
                                <goals>
                                    <goal>sign</goal>
                                </goals>
                                <configuration>
                                    <!-- Explicitly specify the GPG key ID to use -->
                                    <keyname>${gpg.keyname}</keyname>
                                    <!-- Use passphrase from property -->
                                    <passphrase>${gpg.passphrase}</passphrase>
                                    <gpgArguments>
                                        <arg>--pinentry-mode</arg>
                                        <arg>loopback</arg>
                                    </gpgArguments>
                                </configuration>
                            </execution>
                        </executions>
                    </plugin>
                </plugins>
            </build>
        </profile>
        
        <!-- Profile for internal release (with GPG signing) -->
        <profile>
            <id>internal-release</id>
            <build>
                <plugins>
                    <!-- GPG Plugin (Signs artifacts for internal releases) -->
                    <plugin>
                        <groupId>org.apache.maven.plugins</groupId>
                        <artifactId>maven-gpg-plugin</artifactId>
                        <version>3.1.0</version>
                        <executions>
                            <execution>
                                <id>sign-artifacts</id>
                                <phase>verify</phase>
                                <goals>
                                    <goal>sign</goal>
                                </goals>
                                <configuration>
                                    <!-- Explicitly specify the GPG key ID to use -->
                                    <keyname>${gpg.keyname}</keyname>
                                    <!-- Use passphrase from property -->
                                    <passphrase>${gpg.passphrase}</passphrase>
                                    <gpgArguments>
                                        <arg>--pinentry-mode</arg>
                                        <arg>loopback</arg>
                                    </gpgArguments>
                                </configuration>
                            </execution>
                        </executions>
                    </plugin>
                </plugins>
            </build>
        </profile>
        
        <!-- Legacy profile for backward compatibility -->
        <profile>
            <id>sign-artifacts</id>
            <build>
                <plugins>
                    <plugin>
                        <groupId>org.apache.maven.plugins</groupId>
                        <artifactId>maven-gpg-plugin</artifactId>
                        <version>3.1.0</version>
                        <executions>
                            <execution>
                                <id>sign-artifacts</id>
                                <phase>verify</phase>
                                <goals>
                                    <goal>sign</goal>
                                </goals>
                            </execution>
                        </executions>
                    </plugin>
                </plugins>
            </build>
        </profile>
    </profiles>

    <pluginRepositories>
        <pluginRepository>
            <id>central</id>
            <url>https://repo.maven.apache.org/maven2</url>
        </pluginRepository>
    </pluginRepositories>

    <dependencies>
        <dependency>
            <groupId>com.manycoreapis</groupId>
            <artifactId>restapi-sdk-data</artifactId>
            <version>0.0.2</version>
        </dependency>
        <dependency>
            <groupId>com.manycoreapis</groupId>
            <artifactId>geom-data-exchange</artifactId>
            <version>0.0.1</version>
        </dependency>
        {{#swagger1AnnotationLibrary}}
        <dependency>
            <groupId>io.swagger</groupId>
            <artifactId>swagger-annotations</artifactId>
            <version>${swagger-annotations-version}</version>
        </dependency>
        {{/swagger1AnnotationLibrary}}
        {{#swagger2AnnotationLibrary}}
        <dependency>
            <groupId>io.swagger.core.v3</groupId>
            <artifactId>swagger-annotations</artifactId>
            <version>${swagger-annotations-version}</version>
        </dependency>
        {{/swagger2AnnotationLibrary}}

        <!-- @Nullable annotation -->
        <dependency>
            <groupId>com.google.code.findbugs</groupId>
            <artifactId>jsr305</artifactId>
            <version>3.0.2</version>
        </dependency>

        <!-- HTTP client: Spring RestTemplate -->
        <dependency>
            <groupId>org.springframework</groupId>
            <artifactId>spring-web</artifactId>
            <version>${spring-web-version}</version>
        </dependency>
        <dependency>
            <groupId>org.springframework</groupId>
            <artifactId>spring-context</artifactId>
            <version>${spring-web-version}</version>
        </dependency>

        <!-- JSON processing: jackson -->
        <dependency>
            <groupId>com.fasterxml.jackson.core</groupId>
            <artifactId>jackson-core</artifactId>
            <version>${jackson-version}</version>
        </dependency>
        <dependency>
            <groupId>com.fasterxml.jackson.core</groupId>
            <artifactId>jackson-annotations</artifactId>
            <version>${jackson-version}</version>
        </dependency>
        <dependency>
            <groupId>com.fasterxml.jackson.core</groupId>
            <artifactId>jackson-databind</artifactId>
            <version>${jackson-databind-version}</version>
        </dependency>
        {{^useJakartaEe}}
        <dependency>
            <groupId>com.fasterxml.jackson.jaxrs</groupId>
            <artifactId>jackson-jaxrs-json-provider</artifactId>
            <version>${jackson-version}</version>
        </dependency>
        {{/useJakartaEe}}
        {{#useJakartaEe}}
        <dependency>
            <groupId>com.fasterxml.jackson.jakarta.rs</groupId>
            <artifactId>jackson-jakarta-rs-json-provider</artifactId>
            <version>${jackson-version}</version>
        </dependency>
        {{/useJakartaEe}}
        {{#openApiNullable}}
        <dependency>
            <groupId>org.openapitools</groupId>
            <artifactId>jackson-databind-nullable</artifactId>
            <version>${jackson-databind-nullable-version}</version>
        </dependency>
        {{/openApiNullable}}
        {{#withXml}}

        <!-- XML processing: Jackson -->
        <dependency>
            <groupId>com.fasterxml.jackson.dataformat</groupId>
            <artifactId>jackson-dataformat-xml</artifactId>
            <version>${jackson-version}</version>
        </dependency>
        <dependency>
            <groupId>io.github.threeten-jaxb</groupId>
            <artifactId>threeten-jaxb-core</artifactId>
            <version>1.2</version>
        </dependency>
        {{/withXml}}
        <dependency>
            <groupId>com.fasterxml.jackson.datatype</groupId>
            <artifactId>jackson-datatype-jsr310</artifactId>
            <version>${jackson-version}</version>
        </dependency>
        {{#joda}}
        <dependency>
            <groupId>com.fasterxml.jackson.datatype</groupId>
            <artifactId>jackson-datatype-joda</artifactId>
            <version>${jackson-version}</version>
        </dependency>
        <dependency>
            <groupId>joda-time</groupId>
            <artifactId>joda-time</artifactId>
            <version>${jodatime-version}</version>
        </dependency>
        {{/joda}}
      {{#useBeanValidation}}
        <!-- Bean Validation API support -->
        <dependency>
          <groupId>jakarta.validation</groupId>
          <artifactId>jakarta.validation-api</artifactId>
          <version>${beanvalidation-version}</version>
          <scope>provided</scope>
        </dependency>
      {{/useBeanValidation}}
      {{#performBeanValidation}}
        <!-- Bean Validation Impl. used to perform BeanValidation -->
        <dependency>
            <groupId>org.hibernate</groupId>
            <artifactId>hibernate-validator</artifactId>
            <version>${hibernate-validator-version}</version>
        </dependency>
      {{/performBeanValidation}}
        <dependency>
            <groupId>jakarta.annotation</groupId>
            <artifactId>jakarta.annotation-api</artifactId>
            <version>${jakarta-annotation-version}</version>
            <scope>provided</scope>
        </dependency>

        <!-- test dependencies -->
        <dependency>
            <groupId>org.junit.jupiter</groupId>
            <artifactId>junit-jupiter-engine</artifactId>
            <version>${junit-version}</version>
            <scope>test</scope>
        </dependency>
    </dependencies>
    <properties>
        <project.build.sourceEncoding>UTF-8</project.build.sourceEncoding>
        <project.reporting.outputEncoding>UTF-8</project.reporting.outputEncoding>
        {{#swagger1AnnotationLibrary}}
        <swagger-annotations-version>1.6.9</swagger-annotations-version>
        {{/swagger1AnnotationLibrary}}
        {{#swagger2AnnotationLibrary}}
        <swagger-annotations-version>2.2.15</swagger-annotations-version>
        {{/swagger2AnnotationLibrary}}
        <jackson-version>2.17.1</jackson-version>
        <jackson-databind-version>2.17.1</jackson-databind-version>
        {{#openApiNullable}}
        <jackson-databind-nullable-version>0.2.6</jackson-databind-nullable-version>
        {{/openApiNullable}}
        {{#useJakartaEe}}
        <spring-web-version>6.1.14</spring-web-version>
        <jakarta-annotation-version>2.1.1</jakarta-annotation-version>
        <beanvalidation-version>3.0.2</beanvalidation-version>
        {{/useJakartaEe}}
        {{^useJakartaEe}}
        <spring-web-version>5.3.33</spring-web-version>
        <jakarta-annotation-version>1.3.5</jakarta-annotation-version>
        <beanvalidation-version>2.0.2</beanvalidation-version>
        {{/useJakartaEe}}
        {{#joda}}
        <jodatime-version>2.9.9</jodatime-version>
        {{/joda}}
        {{#performBeanValidation}}
        <hibernate-validator-version>5.4.3.Final</hibernate-validator-version>
        {{/performBeanValidation}}
        <junit-version>5.10.2</junit-version>
    </properties>
</project>
