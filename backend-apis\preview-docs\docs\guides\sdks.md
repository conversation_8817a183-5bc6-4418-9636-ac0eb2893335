---
id: sdks
title: SDK 使用指南
sidebar_label: SDK 开发包
---

# SDK 使用指南

群核科技提供多种编程语言的官方 SDK，帮助开发者快速集成我们的 API 服务。使用 SDK 可以大大简化开发流程，并提供更好的类型安全和错误处理。

## 🚀 可用的 SDK

### JavaScript/TypeScript SDK

**适用场景：** Web 应用、Node.js 后端服务

**特性：**
- ✅ 完整的 TypeScript 支持
- ✅ 自动重试和错误处理
- ✅ 内置速率限制
- ✅ Promise/async-await 支持
- ✅ 浏览器和 Node.js 兼容

### Java SDK

**适用场景：** Spring Boot、Android 应用

**特性：**
- ✅ 支持 Java 8+
- ✅ 完整的异常处理
- ✅ 连接池管理
- ✅ 自动序列化/反序列化
- ✅ Maven 和 Gradle 支持

### Python SDK

**适用场景：** Django、Flask、数据分析

**特性：**
- ✅ 支持 Python 3.7+
- ✅ 异步和同步 API
- ✅ 类型提示支持
- ✅ 自动重试机制
- ✅ Pandas 集成

### .NET SDK

**适用场景：** ASP.NET Core、桌面应用

**特性：**
- ✅ 支持 .NET 6+
- ✅ 强类型支持
- ✅ 异步编程模式
- ✅ NuGet 包分发
- ✅ 依赖注入支持

## 📦 安装和配置

### JavaScript/TypeScript SDK {#javascript-sdk}

#### 安装

```bash
npm install @qunhe/api-sdk
# 或者
yarn add @qunhe/api-sdk
```

#### 基础配置

```javascript
import { QunheSdk } from '@qunhe/api-sdk';

const client = new QunheSdk({
  apiKey: process.env.QUNHE_API_KEY,
  environment: 'production', // 或 'sandbox'
  timeout: 30000,
  retries: 3
});
```

#### TypeScript 配置

```typescript
import { QunheSdk, FurnitureData, CreateFurnitureRequest } from '@qunhe/api-sdk';

const client = new QunheSdk({
  apiKey: process.env.QUNHE_API_KEY!,
  environment: 'production'
});

// 类型安全的 API 调用
async function createFurniture(data: CreateFurnitureRequest): Promise<FurnitureData> {
  return await client.furniture.create(data);
}
```

#### 使用示例

```javascript
// 获取家具列表
const furnitureList = await client.furniture.list({
  page: 1,
  pageSize: 20,
  category: 'chair'
});

// 批量获取家具
const furnitureBatch = await client.furniture.batchGet({
  ids: ['furniture1', 'furniture2', 'furniture3']
});

// 创建家具
const newFurniture = await client.furniture.create({
  name: '现代沙发',
  category: 'sofa',
  dimensions: {
    width: 200,
    height: 80,
    depth: 90
  },
  materials: ['fabric', 'wood']
});

// 使用 Webhooks
client.webhooks.verify(payload, signature, secret);
```

### Java SDK {#java-sdk}

#### Maven 配置

```xml
<dependency>
    <groupId>com.qunhe</groupId>
    <artifactId>qunhe-api-sdk</artifactId>
    <version>1.0.0</version>
</dependency>
```

#### Gradle 配置

```gradle
implementation 'com.qunhe:qunhe-api-sdk:1.0.0'
```

#### 基础配置

```java
import com.qunhe.api.QunheClient;
import com.qunhe.api.config.ClientConfig;

public class ApiExample {
    private final QunheClient client;
    
    public ApiExample() {
        ClientConfig config = ClientConfig.builder()
            .apiKey(System.getenv("QUNHE_API_KEY"))
            .environment(Environment.PRODUCTION)
            .timeout(Duration.ofSeconds(30))
            .retries(3)
            .build();
            
        this.client = new QunheClient(config);
    }
}
```

#### 使用示例

```java
// 获取家具列表
FurnitureListRequest request = FurnitureListRequest.builder()
    .page(1)
    .pageSize(20)
    .category("chair")
    .build();

FurnitureListResponse response = client.furniture().list(request);

// 异步调用
CompletableFuture<FurnitureData> future = client.furniture()
    .getAsync("furniture_id");

// 批量操作
List<String> ids = Arrays.asList("id1", "id2", "id3");
BatchGetFurnitureResponse batchResponse = client.furniture()
    .batchGet(BatchGetFurnitureRequest.builder()
        .ids(ids)
        .build());

// 错误处理
try {
    FurnitureData furniture = client.furniture().get("invalid_id");
} catch (ApiException e) {
    System.err.println("API Error: " + e.getCode() + " - " + e.getMessage());
} catch (NetworkException e) {
    System.err.println("Network Error: " + e.getMessage());
}
```

#### Spring Boot 集成

```java
@Configuration
public class QunheConfig {
    
    @Bean
    public QunheClient qunheClient() {
        return new QunheClient(ClientConfig.builder()
            .apiKey("${qunhe.api.key}")
            .environment("${qunhe.api.environment:production}")
            .build());
    }
}

@Service
public class FurnitureService {
    
    @Autowired
    private QunheClient qunheClient;
    
    public List<FurnitureData> getFurnitureList() {
        return qunheClient.furniture()
            .list(FurnitureListRequest.builder()
                .pageSize(50)
                .build())
            .getData();
    }
}
```

### Python SDK {#python-sdk}

#### 安装

```bash
pip install qunhe-api-sdk
```

#### 基础配置

```python
from qunhe_api import QunheClient
import os

client = QunheClient(
    api_key=os.getenv('QUNHE_API_KEY'),
    environment='production',  # 或 'sandbox'
    timeout=30,
    max_retries=3
)
```

#### 同步 API 使用

```python
# 获取家具列表
furniture_list = client.furniture.list(
    page=1,
    page_size=20,
    category='chair'
)

# 获取单个家具
furniture = client.furniture.get('furniture_id')

# 创建家具
new_furniture = client.furniture.create({
    'name': '现代沙发',
    'category': 'sofa',
    'dimensions': {
        'width': 200,
        'height': 80,
        'depth': 90
    }
})

# 批量操作
batch_result = client.furniture.batch_get(['id1', 'id2', 'id3'])
```

#### 异步 API 使用

```python
import asyncio
from qunhe_api import AsyncQunheClient

async def main():
    async_client = AsyncQunheClient(
        api_key=os.getenv('QUNHE_API_KEY')
    )
    
    # 异步获取家具
    furniture = await async_client.furniture.get('furniture_id')
    
    # 并发批量请求
    tasks = [
        async_client.furniture.get(f'furniture_{i}')
        for i in range(1, 6)
    ]
    results = await asyncio.gather(*tasks, return_exceptions=True)
    
    await async_client.close()

# 运行异步代码
asyncio.run(main())
```

#### 错误处理

```python
from qunhe_api.exceptions import (
    ApiException, 
    AuthenticationException, 
    RateLimitException
)

try:
    furniture = client.furniture.get('invalid_id')
except AuthenticationException:
    print("认证失败，请检查 API 密钥")
except RateLimitException as e:
    print(f"速率限制，{e.retry_after} 秒后重试")
except ApiException as e:
    print(f"API 错误: {e.status_code} - {e.message}")
```

#### Django 集成

```python
# settings.py
QUNHE_API_KEY = os.getenv('QUNHE_API_KEY')

# services.py
from django.conf import settings
from qunhe_api import QunheClient

class FurnitureService:
    def __init__(self):
        self.client = QunheClient(api_key=settings.QUNHE_API_KEY)
```

## 🔧 高级功能

### 自定义配置

所有 SDK 都支持丰富的自定义配置：

```javascript
// JavaScript
const client = new QunheSdk({
  apiKey: 'your-api-key',
  baseUrl: 'https://api.kujiale.com',
  timeout: 30000,
  retries: 3,
  retryDelay: 1000,
  
  // 自定义请求拦截器
  requestInterceptor: (config) => {
    config.headers['X-Custom-Header'] = 'value';
    return config;
  },
  
  // 自定义响应拦截器
  responseInterceptor: (response) => {
    console.log('API Response:', response.status);
    return response;
  },
  
  // 日志配置
  logger: {
    level: 'info',
    format: 'json'
  }
});
```

### 代理支持

```python
# Python
client = QunheSdk(
    api_key='your-api-key',
    proxies={
        'http': 'http://proxy.company.com:8080',
        'https': 'https://proxy.company.com:8080'
    }
)
```

```java
// Java
ClientConfig config = ClientConfig.builder()
    .apiKey("your-api-key")
    .proxy(new Proxy(Proxy.Type.HTTP, 
        new InetSocketAddress("proxy.company.com", 8080)))
    .build();
```

### 连接池配置

```java
// Java
ClientConfig config = ClientConfig.builder()
    .apiKey("your-api-key")
    .connectionPoolSize(20)
    .connectionTimeout(Duration.ofSeconds(5))
    .readTimeout(Duration.ofSeconds(30))
    .build();
```

## 🧪 测试支持

### 模拟客户端

```javascript
import { MockQunheClient } from '@qunhe/api-sdk/testing';

const mockClient = new MockQunheClient();

// 设置模拟响应
mockClient.furniture.get.mockResolvedValue({
  id: 'furniture_123',
  name: '测试家具',
  category: 'chair'
});

// 在测试中使用
const furniture = await mockClient.furniture.get('furniture_123');
expect(furniture.name).toBe('测试家具');
```

### 测试工具

```python
# Python
from qunhe_api.testing import MockClient

def test_furniture_service():
    with MockClient() as mock:
        mock.furniture.get.return_value = {
            'id': 'furniture_123',
            'name': '测试家具'
        }
        
        service = FurnitureService(client=mock)
        result = service.get_furniture('furniture_123')
        
        assert result['name'] == '测试家具'
```

## 📊 性能优化

### 连接复用

```javascript
// 复用客户端实例
const client = new QunheSdk({ apiKey: 'your-key' });

// ❌ 避免为每个请求创建新客户端
// const newClient = new QunheSdk({ apiKey: 'your-key' });
```

### 批量操作

```python
# ✅ 推荐：批量获取
furniture_list = client.furniture.batch_get(['id1', 'id2', 'id3'])

# ❌ 避免：多次单独请求
furniture_list = [
    client.furniture.get('id1'),
    client.furniture.get('id2'),
    client.furniture.get('id3')
]
```

### 异步处理

```javascript
// 并发处理多个请求
const promises = ids.map(id => client.furniture.get(id));
const results = await Promise.all(promises);
```

## 🐛 调试和日志

### 启用调试日志

```javascript
// JavaScript
const client = new QunheSdk({
  apiKey: 'your-key',
  debug: true
});
```

## 📞 获取帮助

- **GitHub Issues:** [报告 Bug 或请求功能](https://github.com/qunhe/api-sdk)
- **文档站点:** [查看完整文档](https://docs.qunheco.com)
- **示例项目:** [查看示例代码](https://github.com/qunhe/api-examples)
- **技术支持:** <EMAIL>

## 📈 版本更新

订阅我们的更新通知：

- [GitHub Releases](https://github.com/qunhe/api-sdk/releases)
- [更新日志](/blog)
- [开发者邮件列表](https://developers.qunheco.com/newsletter)