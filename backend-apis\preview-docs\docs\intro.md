---
id: intro
title: 群核科技 API 文档
sidebar_label: 开始使用
slug: /
---

# 群核科技 API 文档中心

欢迎来到群核科技 API 文档中心！这里为开发者提供完整的 API 参考文档、开发指南和示例代码，帮助您快速集成群核科技的全栈家居云设计能力。

## 🎯 平台概览

群核科技（酷家乐）是领先的云端家居设计平台，为设计师、家居企业和开发者提供：

- **3D 设计引擎** - 强大的在线 3D 设计工具
- **海量模型库** - 数百万高质量 3D 家居模型
- **智能设计** - AI 驱动的设计建议和自动化
- **渲染服务** - 云端高性能渲染
- **VR/AR 体验** - 沉浸式虚拟现实展示

## 🚀 核心服务

### 🏠 Camera Infrastructure API
相机基础设施 REST API

为群核旗下多个设计工具提供统一的相机查询和管理功能，支持多种相机类型的预设，
帮助用户在设计过程中快速切换和保存不同的观察角度。

**支持的设计工具：**
详见 `ToolAppType` 枚举定义

**支持的功能：**
- 根据不同设计工具的标识和参数查询相机
- 支持漫游视图、全景图、3D鸟瞰视图
- 相机参数配置管理
- 跨工具的相机数据统一格式

**技术规范：**
- 响应格式：JSON
- 字符编码：UTF-8
- 认证方式：待定


**核心功能：**
- 相机基础设施接口

提供统一的相机查询功能，支持多个设计工具的不同参数需求。
每个相机包含完整的相机参数配置，可用于渲染引擎恢复用户保存的观察状态。

**支持的相机类型：**
- `normal`: 漫游视图 - 用于室内漫游浏览
- `panorama`: 全景图 - 360度全景查看
- `view3d`: 3D鸟瞰视图 - 整体空间俯视

**参数需求：**
- **酷家乐 (kujiale)**: 需要 designId + levelId

  


[查看 Camera Infrastructure API API →](/docs/api/camera/camera)

### 🏠 门窗设计管理API
门窗设计服务REST API

为群核旗下设计工具提供门窗模型的查询和批量更新操作。支持简单门窗（单一门窗单元）和复杂门窗（组合型门窗，如L型、U型等复杂结构）的完整生命周期管理。提供高效的批量操作和异步处理机制，确保大规模门窗数据处理的性能和稳定性。

**核心功能：**
- 门窗文档查询：获取指定楼层的完整门窗信息
- 批量更新操作：支持多种门窗操作类型的批量处理
- 多种门窗类型：简单门、简单窗、复杂门窗组合
- 多视图支持：BASIC、ADVANCED、FULL等不同详细程度的数据视图
- 异步处理：批量操作采用异步机制，保证系统响应性能
- 操作类型丰富：replace（替换）、attach（附加）、detach（分离）、update（更新）

**门窗类型支持：**
- 简单门窗：单一门窗单元，如普通门、普通窗
- 复杂门窗：组合型门窗，如L型窗、U型窗、转角门等
- 门类型：单开门、双开门、推拉门、折叠门等
- 窗类型：平开窗、推拉窗、百叶窗、落地窗等

**业务应用场景：**
- 建筑设计软件的门窗配置
- 门窗产品的三维建模和展示
- 建筑开口与门窗的关联管理
- 门窗规格和参数的批量调整
- 复杂门窗组合的快速创建
- 门窗数据的批量导入和同步

**技术规范：**
- 响应格式：JSON
- 字符编码：UTF-8
- 认证方式：基于设计方案的权限验证
- 批量限制：单次批量操作最多20个门窗
- 异步支持：长时间批量操作的异步处理机制

**核心功能：**
- 门窗设计服务的 REST API，提供门窗模型的查询和批量更新操作。支持简单门窗（单一门窗单元）和复杂门窗（组合型门窗，如L型、U型等复杂结构）的完整生命周期管理。

[查看 门窗设计管理API API →](/docs/api/doorwindow/doorwindow)

### 🏠 家具设计管理API
家具设计服务REST API

为群核旗下设计工具提供家具数据的完整生命周期管理接口，支持家具模型的创建、编辑、查询和管理。提供单个和批量操作，支持不同数据视图以优化性能，适用于各种家具设计和管理场景。

**核心功能：**
- 家具CRUD操作：创建、获取、更新、删除家具实例
- 批量操作支持：高效处理大量家具数据的批量操作
- 多视图支持：BASIC和FULL视图，针对不同场景优化性能
- 分页查询：支持大量家具数据的分页展示和管理
- 产品组合：支持通过组合商品批量创建相关家具
- 异步处理：批量操作采用异步机制，保证系统响应性能
- 幂等性控制：通过requestId确保操作的幂等性

**业务应用场景：**
- 室内设计软件的家具管理
- 家具产品的三维展示和编辑
- 场景模板和套装家具的快速应用
- 家具数据的批量导入和同步
- 个性化家具定制和配置
- 空间布局优化和家具摆放

**技术特性：**
- 响应格式：JSON
- 字符编码：UTF-8
- 认证方式：基于设计方案的权限验证
- 异步支持：长时间批量操作的异步处理机制

**核心功能：**
- 提供家具数据的完整生命周期管理，包括CRUD操作、批量处理、分页查询等功能。支持不同数据视图以优化性能。

[查看 家具设计管理API API →](/docs/api/furniture/furniture)

### 🏠 KooLux REST API
KooLux照明设计的 REST API，提供照明模型、IES的全生命周期管理。  

## 功能特性  
- 支持IES文件的管理  
- 支持照明模型管理  
- 批量操作支持（创建、更新、删除、查询）  

## 数据模型  
- **照明模型**: 支持材质编辑和灯光模型排除  
- **IES光源**: 包含IES文件全量信息


**核心功能：**
- 照明灯光场景编辑接口

[查看 KooLux REST API API →](/docs/api/koolux/koolux)

### 🏠 户型图例管理API
户型图例管理服务REST API

提供图例数据的完整生命周期管理接口，图例是户型设计中用于标识空间元素的核心组件。支持单个和批量操作，包括图例的创建、查询、更新、删除以及复杂的图例组合功能。

**主要功能特性：**
- 单个图例CRUD操作：创建、获取、更新、删除单个图例
- 批量操作支持：高效的批量创建、获取、更新、删除操作
- 分页查询：支持大量图例数据的分页展示和管理
- 图例组合：支持创建图例组合，实现复杂的空间布局
- 异步处理：批量操作采用异步处理机制，确保大数据量操作的性能
- 权限控制：严格的读写权限控制，保障数据安全

**业务场景应用：**
- 户型设计工具中的图例管理
- 空间布局设计和优化
- 图例模板和组合的快速应用
- 户型数据的批量导入导出
- 三维空间定位和图层管理

**技术规范：**
- 响应格式：JSON
- 字符编码：UTF-8
- 认证方式：基于设计方案的权限验证
- 异步操作：支持长时间批量操作的异步处理

**核心功能：**
- 提供图例数据的完整生命周期管理，包括CRUD操作、批量处理、分页查询、图例组合等功能。图例是户型设计中用于标识空间元素的核心组件。

[查看 户型图例管理API API →](/docs/api/layout/layout)

### 🏠 平面造型设计API
平面造型设计REST API

    ## 功能特性
    - 平面造型的 CRUD 操作
    - 批量操作支持（创建、更新、删除、查询）
    - 分页查询支持
    
    ## 数据模型
    - **平面造型**: 包含平面造型所在的面、平面造型形状信息
    
    ## 分页机制
    使用基于页码的分页，支持自定义页面大小

**核心功能：**
- 提供平面造型设计数据的完整生命周期管理，包括CRUD操作、批量处理、分页查询等功能。支持不同数据视图以优化性能。

[查看 平面造型设计API API →](/docs/api/pdm/pdm)

## ⚡ 快速开始

### 1. 获取 API 密钥

首先需要在[开发者控制台](https://developers.qunheco.com)注册并获取 API 密钥。

### 2. 身份认证

所有 API 请求都需要在请求头中包含认证信息：

```http
Authorization: Bearer YOUR_API_KEY
Content-Type: application/json
```

### 3. 发起第一个请求

```bash
curl -X GET \
  'https://api.qunheco.com/camera/v1/endpoint' \
  -H 'Authorization: Bearer YOUR_API_KEY' \
  -H 'Content-Type: application/json'
```

### 4. 集成 SDK

我们提供多种语言的 SDK，简化集成过程：

- [Java SDK](/docs/guides/sdks#java-sdk)
- [JavaScript SDK](/docs/guides/sdks#javascript-sdk)
- [Python SDK](/docs/guides/sdks#python-sdk)

## 📚 学习路径

### 🔰 初学者
1. [身份认证](/docs/getting-started/authentication) - 了解如何认证 API 请求
2. [发起请求](/docs/getting-started/making-requests) - 学习基本的 API 调用
3. [错误处理](/docs/getting-started/error-handling) - 掌握错误处理最佳实践

### 🛠️ 开发者
1. [最佳实践](/docs/guides/best-practices) - API 集成最佳实践
2. [速率限制](/docs/guides/rate-limiting) - 了解 API 使用限制
3. [Webhooks](/docs/guides/webhooks) - 配置事件通知

### 🎨 设计师
1. [最佳实践](/docs/guides/best-practices) - 设计师专用最佳实践
2. [SDK 开发包](/docs/guides/sdks) - 高效使用 SDK 工具
3. [速率限制](/docs/guides/rate-limiting) - 了解使用限制

## 🌐 API 基础信息

| 环境 | 基础 URL | 说明 |
|------|----------|------|
| 生产环境 | `https://api.qunheco.com` | 正式生产环境 |
| 测试环境 | `https://api-test.qunheco.com` | 开发测试使用 |

**API 版本：** v1  
**数据格式：** JSON  
**字符编码：** UTF-8  
**请求方法：** GET, POST, PUT, DELETE  

## 🔗 相关资源

- [开发者门户](https://developers.qunheco.com) - 开发者注册和管理
- [技术博客](https://tech.qunheco.com) - 技术文章和案例分享
- [开发者社区](https://forum.qunheco.com) - 技术讨论和支持
- [状态页面](https://status.qunheco.com) - API 服务状态
- [GitHub](https://github.com/qunhe) - 开源项目和SDK

## 💬 获取帮助

遇到问题？我们提供多种支持渠道：

- **📖 文档搜索** - 使用顶部搜索框快速查找
- **💬 在线客服** - 工作日 9:00-18:00 在线支持
- **📧 邮件支持** - <EMAIL>
- **🎫 工单系统** - 通过开发者控制台提交技术工单

---

**准备好开始了吗？** 👈 从左侧导航开始探索，或直接查看 [API 参考文档](/docs/api) 🚀