'!include': '../../shared/shared-data-mappings.yaml'
generatorName: "java"
inputSpec: "specifications/services/furniture/openapi.yaml"
outputDir: "build/sdks/furniture/java"
templateDir: "generators/sdks/templates/java"
additionalProperties:
  groupId: "com.manycoreapis"
  artifactId: "furniture-rest-client"
  artifactVersion: "0.0.1"
  modelPackage: "com.manycore.furniture.client.model"
  apiPackage: "com.manycore.furniture.client.api"
  invokerPackage: "com.manycore.furniture.client"
  java8: true
  dateLibrary: "java8"
  library: "resttemplate"
  sourceFolder: "src/main/java"
  hideGenerationTimestamp: true
  serializationLibrary: "jackson"
  useRuntimeException: false

    

