#!/bin/bash

# 验证所有镜像引用都来自 qunhe registry 的脚本
# 确保不会意外从 Docker Hub 或其他仓库拉取镜像

set -e

# 颜色输出
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

log_info() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

log_warn() {
    echo -e "${YELLOW}[WARN]${NC} $1"
}

log_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

log_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

# 验证镜像是否来自 qunhe registry
validate_image() {
    local image=$1
    local context=$2
    
    if [[ -z "$image" ]]; then
        log_warn "$context: 镜像为空，跳过检查"
        return 0
    fi
    
    if [[ "$image" =~ ^registry-qunhe.*\.com/ ]]; then
        log_success "$context: ✅ $image (qunhe registry)"
        return 0
    else
        log_error "$context: ❌ $image (非 qunhe registry)"
        return 1
    fi
}

# 主验证函数
main() {
    log_info "=== 验证所有镜像都来自 qunhe registry ==="
    
    local validation_failed=false
    
    # 检查主要环境变量中的镜像
    log_info "检查环境变量中的镜像..."
    
    if ! validate_image "${DOCKER_IMAGE:-}" "DOCKER_IMAGE"; then
        validation_failed=true
    fi
    
    if ! validate_image "${FALLBACK_IMAGE:-}" "FALLBACK_IMAGE"; then
        validation_failed=true
    fi
    
    # 检查 GitLab CI 配置中的默认镜像
    local default_image="registry-qunhe.qunhequnhe.com/display/openapi-generator:latest"
    if ! validate_image "$default_image" "默认镜像"; then
        validation_failed=true
    fi
    
    # 检查是否设置了任何可能指向 Docker Hub 的环境变量
    log_info "检查可能的 Docker Hub 引用..."
    
    local docker_hub_patterns=(
        "docker.io"
        "registry-1.docker.io"
        "index.docker.io"
        "hub.docker.com"
    )
    
    for pattern in "${docker_hub_patterns[@]}"; do
        if env | grep -i "$pattern" >/dev/null 2>&1; then
            log_error "发现 Docker Hub 引用: $(env | grep -i "$pattern")"
            validation_failed=true
        fi
    done
    
    # 检查 Docker 配置
    if [ -f "/etc/docker/daemon.json" ]; then
        log_info "检查 Docker daemon 配置..."
        if grep -q "docker.io\|registry-1.docker.io" /etc/docker/daemon.json 2>/dev/null; then
            log_warn "Docker daemon 配置中包含 Docker Hub 引用"
        fi
    fi
    
    # 总结验证结果
    if [ "$validation_failed" = true ]; then
        log_error "=== 验证失败 ==="
        log_error "发现非 qunhe registry 的镜像引用"
        log_error "请确保所有镜像都来自 qunhe registry"
        return 1
    else
        log_success "=== 验证通过 ==="
        log_success "所有镜像都来自 qunhe registry"
        return 0
    fi
}

# 显示当前镜像配置
show_current_config() {
    log_info "=== 当前镜像配置 ==="
    log_info "DOCKER_IMAGE: ${DOCKER_IMAGE:-未设置}"
    log_info "FALLBACK_IMAGE: ${FALLBACK_IMAGE:-未设置}"
    log_info "DOCKER_REGISTRY: ${DOCKER_REGISTRY:-未设置}"
    
    # 显示所有包含 registry 的环境变量
    log_info "所有 registry 相关环境变量:"
    env | grep -i registry || log_info "无 registry 相关环境变量"
}

# 如果脚本被直接执行
if [ "${BASH_SOURCE[0]}" == "${0}" ]; then
    case "${1:-validate}" in
        "validate")
            main
            ;;
        "show")
            show_current_config
            ;;
        "help")
            echo "用法: $0 [validate|show|help]"
            echo "  validate: 验证所有镜像都来自 qunhe registry (默认)"
            echo "  show: 显示当前镜像配置"
            echo "  help: 显示此帮助信息"
            ;;
        *)
            echo "未知参数: $1"
            echo "使用 '$0 help' 查看帮助"
            exit 1
            ;;
    esac
fi
