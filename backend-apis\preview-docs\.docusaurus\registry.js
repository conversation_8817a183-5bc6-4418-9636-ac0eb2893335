export default {
  "0c7691ae": [() => import(/* webpackChunkName: "0c7691ae" */ "@site/docs/api/doorwindow/门窗管理-api.tag.mdx"), "@site/docs/api/doorwindow/门窗管理-api.tag.mdx", require.resolveWeak("@site/docs/api/doorwindow/门窗管理-api.tag.mdx")],
  "0e384e19": [() => import(/* webpackChunkName: "0e384e19" */ "@site/docs/intro.md"), "@site/docs/intro.md", require.resolveWeak("@site/docs/intro.md")],
  "13a3f6d7": [() => import(/* webpackChunkName: "13a3f6d7" */ "@site/docs/getting-started/making-requests.md"), "@site/docs/getting-started/making-requests.md", require.resolveWeak("@site/docs/getting-started/making-requests.md")],
  "190afcfa": [() => import(/* webpackChunkName: "190afcfa" */ "@site/docs/api/pdm/save-planar-model-v-2.api.mdx"), "@site/docs/api/pdm/save-planar-model-v-2.api.mdx", require.resolveWeak("@site/docs/api/pdm/save-planar-model-v-2.api.mdx")],
  "1a2a7828": [() => import(/* webpackChunkName: "1a2a7828" */ "@site/docs/api/pdm/delete-planar-model.api.mdx"), "@site/docs/api/pdm/delete-planar-model.api.mdx", require.resolveWeak("@site/docs/api/pdm/delete-planar-model.api.mdx")],
  "1a4e3797": [() => import(/* webpackChunkName: "1a4e3797" */ "@theme/SearchPage"), "@theme/SearchPage", require.resolveWeak("@theme/SearchPage")],
  "1bb4a50c": [() => import(/* webpackChunkName: "1bb4a50c" */ "@site/docs/guides/webhooks.md"), "@site/docs/guides/webhooks.md", require.resolveWeak("@site/docs/guides/webhooks.md")],
  "1df93b7f": [() => import(/* webpackChunkName: "1df93b7f" */ "@site/src/pages/index.tsx"), "@site/src/pages/index.tsx", require.resolveWeak("@site/src/pages/index.tsx")],
  "2c2f49e1": [() => import(/* webpackChunkName: "2c2f49e1" */ "@site/docs/api/doorwindow/get-doc.api.mdx"), "@site/docs/api/doorwindow/get-doc.api.mdx", require.resolveWeak("@site/docs/api/doorwindow/get-doc.api.mdx")],
  "2d28a774": [() => import(/* webpackChunkName: "2d28a774" */ "@site/docs/api/pdm/batch-fetch-planar-model-build-result.api.mdx"), "@site/docs/api/pdm/batch-fetch-planar-model-build-result.api.mdx", require.resolveWeak("@site/docs/api/pdm/batch-fetch-planar-model-build-result.api.mdx")],
  "2e5a4e09": [() => import(/* webpackChunkName: "2e5a4e09" */ "@site/docs/api/layout/update-legend.api.mdx"), "@site/docs/api/layout/update-legend.api.mdx", require.resolveWeak("@site/docs/api/layout/update-legend.api.mdx")],
  "2fdb4d27": [() => import(/* webpackChunkName: "2fdb4d27" */ "@site/docs/api/pdm/batch-save-planar-model-v-2.api.mdx"), "@site/docs/api/pdm/batch-save-planar-model-v-2.api.mdx", require.resolveWeak("@site/docs/api/pdm/batch-save-planar-model-v-2.api.mdx")],
  "2fe2be2b": [() => import(/* webpackChunkName: "2fe2be2b" */ "@site/docs/api/furniture/batch-update-furniture.api.mdx"), "@site/docs/api/furniture/batch-update-furniture.api.mdx", require.resolveWeak("@site/docs/api/furniture/batch-update-furniture.api.mdx")],
  "35a134a0": [() => import(/* webpackChunkName: "35a134a0" */ "@site/docs/api/furniture/batch-create-furniture.api.mdx"), "@site/docs/api/furniture/batch-create-furniture.api.mdx", require.resolveWeak("@site/docs/api/furniture/batch-create-furniture.api.mdx")],
  "3addd15c": [() => import(/* webpackChunkName: "3addd15c" */ "@site/docs/api/layout/list-legend.api.mdx"), "@site/docs/api/layout/list-legend.api.mdx", require.resolveWeak("@site/docs/api/layout/list-legend.api.mdx")],
  "3b0bd5ab": [() => import(/* webpackChunkName: "3b0bd5ab" */ "@site/docs/api/layout/get-colored-floor-plan.api.mdx"), "@site/docs/api/layout/get-colored-floor-plan.api.mdx", require.resolveWeak("@site/docs/api/layout/get-colored-floor-plan.api.mdx")],
  "404b8389": [() => import(/* webpackChunkName: "404b8389" */ "@site/docs/api/koolux/koo-lux-light.tag.mdx"), "@site/docs/api/koolux/koo-lux-light.tag.mdx", require.resolveWeak("@site/docs/api/koolux/koo-lux-light.tag.mdx")],
  "41675295": [() => import(/* webpackChunkName: "41675295" */ "@site/docs/api/layout/batch-create-legend-group.api.mdx"), "@site/docs/api/layout/batch-create-legend-group.api.mdx", require.resolveWeak("@site/docs/api/layout/batch-create-legend-group.api.mdx")],
  "48486837": [() => import(/* webpackChunkName: "48486837" */ "@site/docs/api/pdm/平面造型设计管理接口.tag.mdx"), "@site/docs/api/pdm/平面造型设计管理接口.tag.mdx", require.resolveWeak("@site/docs/api/pdm/平面造型设计管理接口.tag.mdx")],
  "4c5e977b": [() => import(/* webpackChunkName: "4c5e977b" */ "@theme/ApiItem"), "@theme/ApiItem", require.resolveWeak("@theme/ApiItem")],
  "521a08f3": [() => import(/* webpackChunkName: "521a08f3" */ "@site/docs/api/camera/camera-infrastructure.tag.mdx"), "@site/docs/api/camera/camera-infrastructure.tag.mdx", require.resolveWeak("@site/docs/api/camera/camera-infrastructure.tag.mdx")],
  "5581d56e": [() => import(/* webpackChunkName: "5581d56e" */ "@site/docs/api/furniture/delete-furniture.api.mdx"), "@site/docs/api/furniture/delete-furniture.api.mdx", require.resolveWeak("@site/docs/api/furniture/delete-furniture.api.mdx")],
  "5e95c892": [() => import(/* webpackChunkName: "5e95c892" */ "@theme/DocsRoot"), "@theme/DocsRoot", require.resolveWeak("@theme/DocsRoot")],
  "5e9f5e1a": [() => import(/* webpackChunkName: "5e9f5e1a" */ "@generated/docusaurus.config"), "@generated/docusaurus.config", require.resolveWeak("@generated/docusaurus.config")],
  "62b36a4a": [() => import(/* webpackChunkName: "62b36a4a" */ "@site/docs/api/layout/户型图例管理api.info.mdx"), "@site/docs/api/layout/户型图例管理api.info.mdx", require.resolveWeak("@site/docs/api/layout/户型图例管理api.info.mdx")],
  "63be01e7": [() => import(/* webpackChunkName: "63be01e7" */ "@site/docs/api/pdm/fetch-planar-model.api.mdx"), "@site/docs/api/pdm/fetch-planar-model.api.mdx", require.resolveWeak("@site/docs/api/pdm/fetch-planar-model.api.mdx")],
  "64d17840": [() => import(/* webpackChunkName: "64d17840" */ "@site/docs/api/furniture/create-furniture.api.mdx"), "@site/docs/api/furniture/create-furniture.api.mdx", require.resolveWeak("@site/docs/api/furniture/create-furniture.api.mdx")],
  "717bcc6b": [() => import(/* webpackChunkName: "717bcc6b" */ "@site/docs/api/furniture/家具管理接口.tag.mdx"), "@site/docs/api/furniture/家具管理接口.tag.mdx", require.resolveWeak("@site/docs/api/furniture/家具管理接口.tag.mdx")],
  "721fefcf": [() => import(/* webpackChunkName: "721fefcf" */ "@site/docs/api/furniture/get-furniture-list.api.mdx"), "@site/docs/api/furniture/get-furniture-list.api.mdx", require.resolveWeak("@site/docs/api/furniture/get-furniture-list.api.mdx")],
  "7dd1a3f2": [() => import(/* webpackChunkName: "7dd1a3f2" */ "@site/docs/api/furniture/batch-create-furniture-by-group-product.api.mdx"), "@site/docs/api/furniture/batch-create-furniture-by-group-product.api.mdx", require.resolveWeak("@site/docs/api/furniture/batch-create-furniture-by-group-product.api.mdx")],
  "89c29901": [() => import(/* webpackChunkName: "89c29901" */ "@site/docs/api/koolux/import-data-source.api.mdx"), "@site/docs/api/koolux/import-data-source.api.mdx", require.resolveWeak("@site/docs/api/koolux/import-data-source.api.mdx")],
  "90381769": [() => import(/* webpackChunkName: "90381769" */ "@site/docs/api/layout/get-single-legend.api.mdx"), "@site/docs/api/layout/get-single-legend.api.mdx", require.resolveWeak("@site/docs/api/layout/get-single-legend.api.mdx")],
  "98258d87": [() => import(/* webpackChunkName: "98258d87" */ "@site/docs/api/furniture/家具设计管理api.info.mdx"), "@site/docs/api/furniture/家具设计管理api.info.mdx", require.resolveWeak("@site/docs/api/furniture/家具设计管理api.info.mdx")],
  "99838d01": [() => import(/* webpackChunkName: "99838d01" */ "@site/docs/api/layout/batch-create-legend.api.mdx"), "@site/docs/api/layout/batch-create-legend.api.mdx", require.resolveWeak("@site/docs/api/layout/batch-create-legend.api.mdx")],
  "a0add546": [() => import(/* webpackChunkName: "a0add546" */ "@site/docs/api/furniture/batch-get-furniture.api.mdx"), "@site/docs/api/furniture/batch-get-furniture.api.mdx", require.resolveWeak("@site/docs/api/furniture/batch-get-furniture.api.mdx")],
  "a2738a55": [() => import(/* webpackChunkName: "a2738a55" */ "@site/docs/guides/best-practices.md"), "@site/docs/guides/best-practices.md", require.resolveWeak("@site/docs/guides/best-practices.md")],
  "a7456010": [() => import(/* webpackChunkName: "a7456010" */ "@generated/docusaurus-plugin-content-pages/default/__plugin.json"), "@generated/docusaurus-plugin-content-pages/default/__plugin.json", require.resolveWeak("@generated/docusaurus-plugin-content-pages/default/__plugin.json")],
  "a77ba7fa": [() => import(/* webpackChunkName: "a77ba7fa" */ "@site/docs/getting-started/authentication.md"), "@site/docs/getting-started/authentication.md", require.resolveWeak("@site/docs/getting-started/authentication.md")],
  "a7bd4aaa": [() => import(/* webpackChunkName: "a7bd4aaa" */ "@theme/DocVersionRoot"), "@theme/DocVersionRoot", require.resolveWeak("@theme/DocVersionRoot")],
  "a7c90080": [() => import(/* webpackChunkName: "a7c90080" */ "@site/docs/api/koolux/koolux-rest-api.info.mdx"), "@site/docs/api/koolux/koolux-rest-api.info.mdx", require.resolveWeak("@site/docs/api/koolux/koolux-rest-api.info.mdx")],
  "a94703ab": [() => import(/* webpackChunkName: "a94703ab" */ "@theme/DocRoot"), "@theme/DocRoot", require.resolveWeak("@theme/DocRoot")],
  "a960dd62": [() => import(/* webpackChunkName: "a960dd62" */ "@site/docs/api/layout/delete-legend.api.mdx"), "@site/docs/api/layout/delete-legend.api.mdx", require.resolveWeak("@site/docs/api/layout/delete-legend.api.mdx")],
  "aa135494": [() => import(/* webpackChunkName: "aa135494" */ "@site/docs/getting-started/error-handling.md"), "@site/docs/getting-started/error-handling.md", require.resolveWeak("@site/docs/getting-started/error-handling.md")],
  "aa419a11": [() => import(/* webpackChunkName: "aa419a11" */ "@site/docs/api/koolux/batch-update.api.mdx"), "@site/docs/api/koolux/batch-update.api.mdx", require.resolveWeak("@site/docs/api/koolux/batch-update.api.mdx")],
  "ab25dcc4": [() => import(/* webpackChunkName: "ab25dcc4" */ "@site/docs/api/layout/batch-get-legend.api.mdx"), "@site/docs/api/layout/batch-get-legend.api.mdx", require.resolveWeak("@site/docs/api/layout/batch-get-legend.api.mdx")],
  "aba21aa0": [() => import(/* webpackChunkName: "aba21aa0" */ "@generated/docusaurus-plugin-content-docs/default/__plugin.json"), "@generated/docusaurus-plugin-content-docs/default/__plugin.json", require.resolveWeak("@generated/docusaurus-plugin-content-docs/default/__plugin.json")],
  "aee2c0a2": [() => import(/* webpackChunkName: "aee2c0a2" */ "@site/docs/api/layout/create-legend.api.mdx"), "@site/docs/api/layout/create-legend.api.mdx", require.resolveWeak("@site/docs/api/layout/create-legend.api.mdx")],
  "bfb852c9": [() => import(/* webpackChunkName: "bfb852c9" */ "@site/docs/api/furniture/batch-delete-furniture.api.mdx"), "@site/docs/api/furniture/batch-delete-furniture.api.mdx", require.resolveWeak("@site/docs/api/furniture/batch-delete-furniture.api.mdx")],
  "c141421f": [() => import(/* webpackChunkName: "c141421f" */ "@generated/docusaurus-theme-search-algolia/default/__plugin.json"), "@generated/docusaurus-theme-search-algolia/default/__plugin.json", require.resolveWeak("@generated/docusaurus-theme-search-algolia/default/__plugin.json")],
  "c5562c0d": [() => import(/* webpackChunkName: "c5562c0d" */ "@site/docs/api/koolux/get-scene-document.api.mdx"), "@site/docs/api/koolux/get-scene-document.api.mdx", require.resolveWeak("@site/docs/api/koolux/get-scene-document.api.mdx")],
  "c920ddb4": [() => import(/* webpackChunkName: "c920ddb4" */ "@site/docs/api/furniture/get-single-furniture.api.mdx"), "@site/docs/api/furniture/get-single-furniture.api.mdx", require.resolveWeak("@site/docs/api/furniture/get-single-furniture.api.mdx")],
  "ca81dce7": [() => import(/* webpackChunkName: "ca81dce7" */ "@site/docs/api/pdm/平面造型设计api.info.mdx"), "@site/docs/api/pdm/平面造型设计api.info.mdx", require.resolveWeak("@site/docs/api/pdm/平面造型设计api.info.mdx")],
  "cbc06501": [() => import(/* webpackChunkName: "cbc06501" */ "@site/docs/api/layout/图例管理接口.tag.mdx"), "@site/docs/api/layout/图例管理接口.tag.mdx", require.resolveWeak("@site/docs/api/layout/图例管理接口.tag.mdx")],
  "d18a7eaf": [() => import(/* webpackChunkName: "d18a7eaf" */ "@site/docs/guides/sdks.md"), "@site/docs/guides/sdks.md", require.resolveWeak("@site/docs/guides/sdks.md")],
  "d1de8a95": [() => import(/* webpackChunkName: "d1de8a95" */ "@site/docs/guides/rate-limiting.md"), "@site/docs/guides/rate-limiting.md", require.resolveWeak("@site/docs/guides/rate-limiting.md")],
  "d3d436f2": [() => import(/* webpackChunkName: "d3d436f2" */ "@site/docs/api/doorwindow/门窗设计管理api.info.mdx"), "@site/docs/api/doorwindow/门窗设计管理api.info.mdx", require.resolveWeak("@site/docs/api/doorwindow/门窗设计管理api.info.mdx")],
  "d9b962e5": [() => import(/* webpackChunkName: "d9b962e5" */ "@site/docs/api/camera/camera-infrastructure-api.info.mdx"), "@site/docs/api/camera/camera-infrastructure-api.info.mdx", require.resolveWeak("@site/docs/api/camera/camera-infrastructure-api.info.mdx")],
  "db4aaba5": [() => import(/* webpackChunkName: "db4aaba5" */ "@site/docs/api/pdm/fetch-planar-model-list.api.mdx"), "@site/docs/api/pdm/fetch-planar-model-list.api.mdx", require.resolveWeak("@site/docs/api/pdm/fetch-planar-model-list.api.mdx")],
  "e2064f97": [() => import(/* webpackChunkName: "e2064f97" */ "@site/docs/api/pdm/batch-delete-planar-model.api.mdx"), "@site/docs/api/pdm/batch-delete-planar-model.api.mdx", require.resolveWeak("@site/docs/api/pdm/batch-delete-planar-model.api.mdx")],
  "e8ed10da": [() => import(/* webpackChunkName: "e8ed10da" */ "@site/docs/api/furniture/update-furniture.api.mdx"), "@site/docs/api/furniture/update-furniture.api.mdx", require.resolveWeak("@site/docs/api/furniture/update-furniture.api.mdx")],
  "eb1641ef": [() => import(/* webpackChunkName: "eb1641ef" */ "@generated/docusaurus-plugin-content-docs/default/p/manycoreapi-demo-0-0-4-docs-513.json"), "@generated/docusaurus-plugin-content-docs/default/p/manycoreapi-demo-0-0-4-docs-513.json", require.resolveWeak("@generated/docusaurus-plugin-content-docs/default/p/manycoreapi-demo-0-0-4-docs-513.json")],
  "f0370a01": [() => import(/* webpackChunkName: "f0370a01" */ "@site/docs/api/layout/batch-update-legend.api.mdx"), "@site/docs/api/layout/batch-update-legend.api.mdx", require.resolveWeak("@site/docs/api/layout/batch-update-legend.api.mdx")],
  "f32cea21": [() => import(/* webpackChunkName: "f32cea21" */ "@site/docs/api/camera/get-cameras.api.mdx"), "@site/docs/api/camera/get-cameras.api.mdx", require.resolveWeak("@site/docs/api/camera/get-cameras.api.mdx")],
  "f3dcbab1": [() => import(/* webpackChunkName: "f3dcbab1" */ "@site/docs/api/doorwindow/batch-update.api.mdx"), "@site/docs/api/doorwindow/batch-update.api.mdx", require.resolveWeak("@site/docs/api/doorwindow/batch-update.api.mdx")],
  "f8bd8094": [() => import(/* webpackChunkName: "f8bd8094" */ "@site/docs/api/layout/batch-delete-legend.api.mdx"), "@site/docs/api/layout/batch-delete-legend.api.mdx", require.resolveWeak("@site/docs/api/layout/batch-delete-legend.api.mdx")],
  "f9f60f8e": [() => import(/* webpackChunkName: "f9f60f8e" */ "@site/docs/api/pdm/batch-fetch-planar-model.api.mdx"), "@site/docs/api/pdm/batch-fetch-planar-model.api.mdx", require.resolveWeak("@site/docs/api/pdm/batch-fetch-planar-model.api.mdx")],};


