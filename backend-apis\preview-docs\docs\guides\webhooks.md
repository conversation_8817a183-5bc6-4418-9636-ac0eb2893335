---
id: webhooks
title: Webhooks 事件通知
sidebar_label: Webhooks
---

# Webhooks 事件通知

Webhooks 允许群核科技在特定事件发生时主动向您的应用发送 HTTP 请求，而无需您持续轮询我们的 API。这是一种高效的实时通知机制。

## 🎯 什么是 Webhooks

Webhooks 是一种"反向 API"模式：

- 🔄 **传统 API**：您的应用主动请求我们的服务器
- 📨 **Webhooks**：我们的服务器主动通知您的应用

### 使用场景

- 📦 模型状态更新通知
- 🏠 设计方案完成通知
- 💰 订单状态变更通知
- 👤 用户权限变更通知
- 🔧 系统维护通知

## 📋 支持的事件类型

### 模型相关事件

| 事件类型 | 描述 | 数据包含 |
|----------|------|----------|
| `model.created` | 新模型创建完成 | 模型ID、名称、分类 |
| `model.updated` | 模型信息更新 | 模型ID、更新字段 |
| `model.deleted` | 模型被删除 | 模型ID、删除时间 |
| `model.published` | 模型发布上线 | 模型ID、发布状态 |

### 设计相关事件

| 事件类型 | 描述 | 数据包含 |
|----------|------|----------|
| `design.created` | 新设计方案创建 | 设计ID、用户ID |
| `design.rendered` | 设计渲染完成 | 设计ID、渲染图URL |
| `design.shared` | 设计方案被分享 | 设计ID、分享链接 |

### 订单相关事件

| 事件类型 | 描述 | 数据包含 |
|----------|------|----------|
| `order.created` | 新订单创建 | 订单ID、金额、商品 |
| `order.paid` | 订单支付完成 | 订单ID、支付时间 |
| `order.shipped` | 订单发货 | 订单ID、物流信息 |
| `order.completed` | 订单完成 | 订单ID、完成时间 |

### 用户相关事件

| 事件类型 | 描述 | 数据包含 |
|----------|------|----------|
| `user.registered` | 用户注册 | 用户ID、注册时间 |
| `user.subscription_changed` | 订阅计划变更 | 用户ID、新计划 |
| `user.quota_exceeded` | API 配额超限 | 用户ID、配额信息 |

## ⚙️ 配置 Webhooks

### 1. 在开发者控制台配置

1. 登录 [开发者控制台](https://developers.qunheco.com)
2. 选择您的应用
3. 进入"Webhooks"设置页面
4. 点击"添加 Webhook"

### 2. 基本配置

```json
{
  "url": "https://your-domain.com/webhooks/kujiale",
  "events": [
    "model.created",
    "model.updated",
    "design.rendered"
  ],
  "secret": "your-webhook-secret",
  "description": "生产环境 Webhook",
  "active": true
}
```

### 3. 高级配置

```json
{
  "url": "https://your-domain.com/webhooks/kujiale",
  "events": ["*"], // 订阅所有事件
  "secret": "your-webhook-secret",
  "headers": {
    "X-Custom-Header": "custom-value"
  },
  "timeout": 30, // 秒
  "retry_policy": {
    "max_attempts": 3,
    "backoff_type": "exponential"
  },
  "filters": {
    "user_id": ["user123", "user456"], // 只接收特定用户的事件
    "category": ["furniture", "decoration"]
  }
}
```

## 📨 接收 Webhook 事件

### 请求格式

每个 Webhook 请求都包含以下内容：

```http
POST /webhooks/kujiale HTTP/1.1
Host: your-domain.com
Content-Type: application/json
X-Kujiale-Event: model.created
X-Kujiale-Signature: sha256=abc123...
X-Kujiale-Delivery: 12345678-1234-1234-1234-123456789abc
User-Agent: Kujiale-Webhooks/1.0

{
  "id": "evt_1234567890",
  "type": "model.created",
  "created": 1640995200,
  "data": {
    "object": {
      "id": "model_abc123",
      "name": "现代沙发",
      "category": "furniture",
      "status": "published",
      "created_at": "2024-01-01T10:00:00Z",
      "updated_at": "2024-01-01T10:00:00Z",
      "user_id": "user_123",
      "metadata": {
        "source": "api",
        "version": "1.0"
      }
    }
  }
}
```

### 重要请求头

| 请求头 | 描述 |
|--------|------|
| `X-Kujiale-Event` | 事件类型 |
| `X-Kujiale-Signature` | 签名验证 |
| `X-Kujiale-Delivery` | 唯一投递ID |
| `X-Kujiale-Timestamp` | 事件时间戳 |

## 🔐 验证 Webhook 签名

为确保请求来自群核科技，每个 Webhook 都包含 HMAC SHA256 签名。

### Node.js 验证示例

```javascript
const crypto = require('crypto');

function verifyWebhookSignature(payload, signature, secret) {
  const expectedSignature = crypto
    .createHmac('sha256', secret)
    .update(payload, 'utf8')
    .digest('hex');
  
  const receivedSignature = signature.replace('sha256=', '');
  
  return crypto.timingSafeEqual(
    Buffer.from(expectedSignature, 'hex'),
    Buffer.from(receivedSignature, 'hex')
  );
}

// Express.js 中间件
function webhookVerifier(req, res, next) {
  const signature = req.headers['x-kujiale-signature'];
  const payload = JSON.stringify(req.body);
  const secret = process.env.WEBHOOK_SECRET;
  
  if (!verifyWebhookSignature(payload, signature, secret)) {
    return res.status(401).json({ error: 'Invalid signature' });
  }
  
  next();
}
```

### Python 验证示例

```python
import hmac
import hashlib

def verify_webhook_signature(payload, signature, secret):
    expected_signature = hmac.new(
        secret.encode('utf-8'),
        payload.encode('utf-8'),
        hashlib.sha256
    ).hexdigest()
    
    received_signature = signature.replace('sha256=', '')
    
    return hmac.compare_digest(expected_signature, received_signature)

# Django 视图示例
from django.http import JsonResponse
from django.views.decorators.csrf import csrf_exempt
import json

@csrf_exempt
def webhook_handler(request):
    if request.method != 'POST':
        return JsonResponse({'error': 'Method not allowed'}, status=405)
    
    signature = request.headers.get('X-Kujiale-Signature')
    payload = request.body.decode('utf-8')
    secret = settings.WEBHOOK_SECRET
    
    if not verify_webhook_signature(payload, signature, secret):
        return JsonResponse({'error': 'Invalid signature'}, status=401)
    
    data = json.loads(payload)
    # 处理事件
    handle_webhook_event(data)
    
    return JsonResponse({'status': 'success'})
```

### Java 验证示例

```java
import javax.crypto.Mac;
import javax.crypto.spec.SecretKeySpec;
import java.security.MessageDigest;

public class WebhookVerifier {
    
    public static boolean verifySignature(String payload, String signature, String secret) {
        try {
            Mac mac = Mac.getInstance("HmacSHA256");
            SecretKeySpec secretKeySpec = new SecretKeySpec(secret.getBytes(), "HmacSHA256");
            mac.init(secretKeySpec);
            
            byte[] hash = mac.doFinal(payload.getBytes());
            String expectedSignature = bytesToHex(hash);
            String receivedSignature = signature.replace("sha256=", "");
            
            return MessageDigest.isEqual(
                expectedSignature.getBytes(),
                receivedSignature.getBytes()
            );
        } catch (Exception e) {
            return false;
        }
    }
    
    private static String bytesToHex(byte[] bytes) {
        StringBuilder result = new StringBuilder();
        for (byte b : bytes) {
            result.append(String.format("%02x", b));
        }
        return result.toString();
    }
}

// Spring Boot 控制器
@RestController
@RequestMapping("/webhooks")
public class WebhookController {
    
    @Value("${webhook.secret}")
    private String webhookSecret;
    
    @PostMapping("/kujiale")
    public ResponseEntity<String> handleWebhook(
            @RequestBody String payload,
            @RequestHeader("X-Kujiale-Signature") String signature) {
        
        if (!WebhookVerifier.verifySignature(payload, signature, webhookSecret)) {
            return ResponseEntity.status(401).body("Invalid signature");
        }
        
        // 处理事件
        processWebhookEvent(payload);
        
        return ResponseEntity.ok("Success");
    }
}
```

## 🛠️ 处理 Webhook 事件

### 事件路由

```javascript
function handleWebhookEvent(event) {
  switch (event.type) {
    case 'model.created':
      handleModelCreated(event.data.object);
      break;
    case 'model.updated':
      handleModelUpdated(event.data.object);
      break;
    case 'design.rendered':
      handleDesignRendered(event.data.object);
      break;
    case 'order.paid':
      handleOrderPaid(event.data.object);
      break;
    default:
      console.log(`Unhandled event type: ${event.type}`);
  }
}

async function handleModelCreated(model) {
  // 新模型创建后的业务逻辑
  console.log(`新模型创建: ${model.name} (${model.id})`);
  
  // 发送通知邮件
  await sendNotificationEmail({
    to: '<EMAIL>',
    subject: '新模型上线',
    template: 'model-created',
    data: model
  });
  
  // 更新本地数据库
  await updateLocalModelCache(model);
}

async function handleDesignRendered(design) {
  // 设计渲染完成后的处理
  console.log(`设计渲染完成: ${design.id}`);
  
  // 下载渲染图到本地存储
  await downloadRenderImage(design.render_url, design.id);
  
  // 通知用户
  await notifyUser(design.user_id, 'design-rendered', design);
}
```

### 幂等性处理

由于网络问题，可能会收到重复的 Webhook 事件。需要确保处理逻辑是幂等的：

```javascript
const processedEvents = new Set();

function handleWebhookEvent(event) {
  // 检查是否已处理过此事件
  if (processedEvents.has(event.id)) {
    console.log(`Event ${event.id} already processed, skipping...`);
    return;
  }
  
  try {
    // 处理事件逻辑
    processEvent(event);
    
    // 标记为已处理
    processedEvents.add(event.id);
  } catch (error) {
    console.error(`Error processing event ${event.id}:`, error);
    throw error;
  }
}

// 使用数据库存储已处理的事件ID
async function handleWebhookEventWithDB(event) {
  const existingEvent = await db.webhookEvents.findOne({ id: event.id });
  if (existingEvent) {
    return; // 已处理过
  }
  
  await db.webhookEvents.create({
    id: event.id,
    type: event.type,
    processed_at: new Date()
  });
  
  // 处理事件逻辑
  await processEvent(event);
}
```

## 🔄 重试机制

### 群核科技的重试策略

当您的端点返回非 2xx 状态码时，我们会按以下策略重试：

- **重试次数**：最多 3 次
- **重试间隔**：1秒、3秒、9秒（指数退避）
- **超时时间**：30秒

### 正确的响应

```javascript
// ✅ 正确：快速响应 200
app.post('/webhooks/kujiale', (req, res) => {
  // 立即响应 200
  res.status(200).json({ received: true });
  
  // 异步处理事件
  setImmediate(() => {
    processWebhookEvent(req.body);
  });
});

// ❌ 错误：长时间处理后才响应
app.post('/webhooks/kujiale', async (req, res) => {
  try {
    await longRunningTask(req.body); // 这可能超时
    res.status(200).json({ success: true });
  } catch (error) {
    res.status(500).json({ error: error.message });
  }
});
```

### 处理重试

```javascript
app.post('/webhooks/kujiale', (req, res) => {
  const deliveryId = req.headers['x-kujiale-delivery'];
  
  // 检查是否为重试请求
  if (isRetryRequest(deliveryId)) {
    console.log(`Retry request detected: ${deliveryId}`);
  }
  
  res.status(200).json({ received: true });
  
  // 异步处理，确保幂等性
  processWebhookSafely(req.body);
});
```

## 📊 监控和调试

### 日志记录

```javascript
const winston = require('winston');

const webhookLogger = winston.createLogger({
  format: winston.format.json(),
  transports: [
    new winston.transports.File({ filename: 'webhook-errors.log', level: 'error' }),
    new winston.transports.File({ filename: 'webhooks.log' })
  ]
});

app.post('/webhooks/kujiale', (req, res) => {
  const event = req.body;
  const deliveryId = req.headers['x-kujiale-delivery'];
  
  webhookLogger.info('Webhook received', {
    event_id: event.id,
    event_type: event.type,
    delivery_id: deliveryId,
    timestamp: new Date().toISOString()
  });
  
  try {
    processWebhookEvent(event);
    
    webhookLogger.info('Webhook processed successfully', {
      event_id: event.id,
      delivery_id: deliveryId
    });
    
    res.status(200).json({ success: true });
  } catch (error) {
    webhookLogger.error('Webhook processing failed', {
      event_id: event.id,
      delivery_id: deliveryId,
      error: error.message,
      stack: error.stack
    });
    
    res.status(500).json({ error: 'Processing failed' });
  }
});
```

### 健康检查端点

```javascript
app.get('/webhooks/health', (req, res) => {
  res.status(200).json({
    status: 'healthy',
    timestamp: new Date().toISOString(),
    webhook_secret_configured: !!process.env.WEBHOOK_SECRET
  });
});
```

## 🧪 测试 Webhooks

### 使用 ngrok 进行本地测试

```bash
# 安装 ngrok
npm install -g ngrok

# 启动本地服务器
node server.js

# 在另一个终端中启动 ngrok
ngrok http 3000

# 使用生成的 URL 配置 Webhook
# 例如：https://abc123.ngrok.io/webhooks/kujiale
```

### 模拟 Webhook 事件

```javascript
// 测试脚本
const crypto = require('crypto');

function createTestWebhook(event, secret) {
  const payload = JSON.stringify(event);
  const signature = 'sha256=' + crypto
    .createHmac('sha256', secret)
    .update(payload)
    .digest('hex');
  
  return {
    payload,
    headers: {
      'Content-Type': 'application/json',
      'X-Kujiale-Event': event.type,
      'X-Kujiale-Signature': signature,
      'X-Kujiale-Delivery': 'test-delivery-id'
    }
  };
}

// 发送测试事件
const testEvent = {
  id: 'evt_test_123',
  type: 'model.created',
  created: Math.floor(Date.now() / 1000),
  data: {
    object: {
      id: 'model_test_456',
      name: '测试模型',
      category: 'furniture'
    }
  }
};

const { payload, headers } = createTestWebhook(testEvent, 'your-secret');

// 使用 axios 发送到本地服务器
axios.post('http://localhost:3000/webhooks/kujiale', JSON.parse(payload), {
  headers
}).then(response => {
  console.log('Test webhook sent successfully');
}).catch(error => {
  console.error('Test webhook failed:', error);
});
```

## ⚠️ 常见问题

### 1. 签名验证失败

```javascript
// 常见错误：使用错误的数据进行签名验证
❌ const payload = req.body; // 对象
✅ const payload = JSON.stringify(req.body); // 字符串

// 常见错误：使用处理过的数据
❌ const payload = req.body; // Express 已解析的对象
✅ const payload = req.rawBody; // 原始字符串
```

### 2. 响应超时

```javascript
// ❌ 错误：在响应前进行长时间处理
app.post('/webhook', async (req, res) => {
  await longRunningTask(); // 可能超时
  res.status(200).send('OK');
});

// ✅ 正确：立即响应，异步处理
app.post('/webhook', (req, res) => {
  res.status(200).send('OK');
  setImmediate(() => longRunningTask());
});
```

### 3. 重复处理

```javascript
// ✅ 使用唯一ID确保幂等性
const processedEvents = new Set();

function handleEvent(event) {
  if (processedEvents.has(event.id)) {
    return; // 已处理
  }
  
  // 处理逻辑
  processEvent(event);
  processedEvents.add(event.id);
}
```

## 📚 相关资源

- [开发者控制台](https://developers.qunheco.com) - 配置和管理 Webhooks
- [最佳实践](/docs/guides/best-practices) - Webhooks 使用最佳实践
- [速率限制](/docs/guides/rate-limiting) - 了解 API 限制

---

需要帮助？联系我们的技术支持：<EMAIL> 