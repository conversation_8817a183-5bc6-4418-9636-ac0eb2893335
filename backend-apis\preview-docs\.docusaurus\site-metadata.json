{"docusaurusVersion": "3.8.1", "siteVersion": "1.0.0", "pluginVersions": {"docusaurus-plugin-css-cascade-layers": {"type": "package", "name": "@docusaurus/plugin-css-cascade-layers", "version": "3.8.1"}, "docusaurus-plugin-content-docs": {"type": "package", "name": "@docusaurus/plugin-content-docs", "version": "3.8.1"}, "docusaurus-plugin-content-pages": {"type": "package", "name": "@docusaurus/plugin-content-pages", "version": "3.8.1"}, "docusaurus-plugin-sitemap": {"type": "package", "name": "@docusaurus/plugin-sitemap", "version": "3.8.1"}, "docusaurus-plugin-svgr": {"type": "package", "name": "@docusaurus/plugin-svgr", "version": "3.8.1"}, "docusaurus-theme-classic": {"type": "package", "name": "@docusaurus/theme-classic", "version": "3.8.1"}, "docusaurus-theme-search-algolia": {"type": "package", "name": "@docusaurus/theme-search-algolia", "version": "3.8.1"}, "docusaurus-plugin-openapi-docs": {"type": "package", "name": "docusaurus-plugin-openapi-docs", "version": "4.5.1"}, "docusaurus-theme-openapi": {"type": "package", "name": "docusaurus-theme-openapi-docs", "version": "4.5.1"}}}