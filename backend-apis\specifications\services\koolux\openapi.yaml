openapi: 3.1.0
info:
  title: KooLux REST API
  description: |
        KooLux照明设计的 REST API，提供照明模型、IES的全生命周期管理。  
    
        ## 功能特性  
        - 支持IES文件的管理  
        - 支持照明模型管理  
        - 批量操作支持（创建、更新、删除、查询）  
    
        ## 数据模型  
        - **照明模型**: 支持材质编辑和灯光模型排除  
        - **IES光源**: 包含IES文件全量信息
  contact:
    name: 照明设计团队
    email: <EMAIL>
  license:
    name: 内部使用
    url: 'https://www.manycore.com'
  version: 1.0.0
servers:
  - url: 'https://www.koolux.com'
    description: KooLux官网地址
tags:
  - name: KooLuxLight
    description: 照明灯光场景编辑接口
paths:
  '/koolux/openapi/rest/v1/light:importdatasource':
    post:
      tags:
        - KooLuxLight
      summary: 创建KooLux方案
      description: 导入酷家乐方案到KooLux
      operationId: importDataSource
      requestBody:
        description: create koolux design request data
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/CreateDesignRequest'
        required: true
      responses:
        '200':
          description: Success
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/CreateDesignResponse'
        '400':
          description: Invalid Argument
          content:
            '*/*':
              schema:
                $ref: '../../common/restapi-sdk-data-schemas.yaml#/components/schemas/ApiError'
        '401':
          description: Unauthenticated
          content:
            '*/*':
              schema:
                $ref: '../../common/restapi-sdk-data-schemas.yaml#/components/schemas/ApiError'
        '404':
          description: Design not found
          content:
            '*/*':
              schema:
                $ref: '../../common/restapi-sdk-data-schemas.yaml#/components/schemas/ApiError'
        '500':
          description: Internal Server Error
          content:
            '*/*':
              schema:
                $ref: '../../common/restapi-sdk-data-schemas.yaml#/components/schemas/ApiError'
      x-return-extra:
        is-operation: 'true'
  '/koolux/openapi/rest/v1/designs/{kooLuxDesignId}/light':
    get:
      tags:
        - KooLuxLight
      summary: 查询照明场景数据（建设中，暂不可用）
      description: 查询照明场景中的模型、ies灯光数据
      operationId: getSceneDocument
      parameters:
        - name: kooLuxDesignId
          in: path
          description: encrypted koolux design id
          required: true
          schema:
            type: string
      responses:
        '200':
          description: Success
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/SceneDocument'
        '401':
          description: Unauthenticated
          content:
            '*/*':
              schema:
                $ref: '../../common/restapi-sdk-data-schemas.yaml#/components/schemas/ApiError'
        '404':
          description: Design not found
          content:
            '*/*':
              schema:
                $ref: '../../common/restapi-sdk-data-schemas.yaml#/components/schemas/ApiError'
  '/koolux/openapi/rest/v1/designs/{kooLuxDesignId}/light:batchupdate':
    put:
      tags:
        - KooLuxLight
      summary: 批量更新照明场景数据
      description: 批量更新照明场景中的模型、ies灯光数据，只针对照明对象生效
      operationId: batchUpdate
      parameters:
        - name: kooLuxDesignId
          in: path
          description: encrypted koolux design id
          required: true
          schema:
            type: string
      requestBody:
        description: batch update light request data
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/SceneDocumentBatchUpdateRequest'
        required: true
      responses:
        '200':
          description: Success
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/SceneDocumentBatchUpdateResponse'
        '400':
          description: Invalid Argument
          content:
            '*/*':
              schema:
                $ref: '../../common/restapi-sdk-data-schemas.yaml#/components/schemas/ApiError'
        '401':
          description: Unauthenticated
          content:
            '*/*':
              schema:
                $ref: '../../common/restapi-sdk-data-schemas.yaml#/components/schemas/ApiError'
        '404':
          description: Model or Light not found
          content:
            '*/*':
              schema:
                $ref: '../../common/restapi-sdk-data-schemas.yaml#/components/schemas/ApiError'
        '500':
          description: Internal Server Error
          content:
            '*/*':
              schema:
                $ref: '../../common/restapi-sdk-data-schemas.yaml#/components/schemas/ApiError'
      x-return-extra:
        is-operation: 'true'
components:
  schemas:
    CreateDesignRequest:
      required:
        - bizTypeId
        - dataSourceId
        - type
      type: object
      properties:
        requestId:
          type: string
          description: 请求ID，用于幂等性控制
        operationId:
          type: string
          description: 操作ID，用于异步操作跟踪
        dataSourceId:
          type: string
          description: 数据源ID（如酷家乐方案，则是酷家乐的designId）
        type:
          type: string
          description: 数据源类型，UNSPECIFIED(未定义)，KJL(酷家乐)
          enum:
            - UNSPECIFIED
            - KJL
        bizTypeId:
          type: integer
          description: 业务线ID，联系KooLux分配
          format: int32
    CreateDesignResponse:
      type: object
      properties:
        dataSourceId:
          type: string
          description: 数据源ID（如酷家乐方案，则是酷家乐的designId）
        type:
          type: string
          description: 数据源类型，UNSPECIFIED(未定义)，KJL(酷家乐)
          enum:
            - UNSPECIFIED
            - KJL
        kooLuxDesignId:
          type: string
          description: KooLux方案ID
    IesSpotLightData:
      type: object
      properties:
        iesId:
          type: string
          description: 用户上传ies id（新增灯光时，iesId和iesFile必传其一）
        iesFile:
          type: string
          description: ies file路径（新增灯光时，iesId和iesFile必传其一）
        brightness:
          type: number
          description: 亮度百分比，0~1（默认0）
          format: double
        transform:
          $ref: '#/components/schemas/Matrix'
        maintenanceFactor:
          type: number
          description: 维护系数，0~1（默认1）
          format: float
        spectrumData:
          $ref: '#/components/schemas/SpectrumData'
      description: ies光源数据
    LightCreateRequest:
      required:
        - iesSpotLightData
      type: object
      description: 添加光源
      allOf:
        - $ref: '#/components/schemas/SceneDocumentOperateRequest'
        - type: object
          properties:
            iesSpotLightData:
              $ref: '#/components/schemas/IesSpotLightData'
            bindingModelIds:
              type: array
              description: ies绑定的模型ID列表
              items:
                type: string
                description: ies绑定的模型ID列表
    LightDeleteRequest:
      required:
        - id
      type: object
      description: 删除光源
      allOf:
        - $ref: '#/components/schemas/SceneDocumentOperateRequest'
        - type: object
          properties:
            id:
              type: string
              description: 灯光ID
    LightUpdateRequest:
      required:
        - id
        - iesSpotLightData
      type: object
      description: 更新光源
      allOf:
        - $ref: '#/components/schemas/SceneDocumentOperateRequest'
        - type: object
          properties:
            id:
              type: string
              description: 灯光ID
            iesSpotLightData:
              $ref: '#/components/schemas/IesSpotLightData'
    Matrix:
      type: object
      properties:
        elements:
          type: array
          items:
            type: number
            format: double
      description: transform信息
    ModelCreateRequest:
      required:
        - id
        - productId
        - transform
      type: object
      description: 添加模型
      allOf:
        - $ref: '#/components/schemas/SceneDocumentOperateRequest'
        - type: object
          properties:
            id:
              type: string
              description: 唯一ID，用uuid生成，和ies的绑定关系需要用到该ID
            productId:
              type: string
              description: 商品ID
            transform:
              $ref: '#/components/schemas/Matrix'
    ModelDeleteRequest:
      required:
        - id
      type: object
      description: 删除模型
      allOf:
        - $ref: '#/components/schemas/SceneDocumentOperateRequest'
        - type: object
          properties:
            id:
              type: string
              description: 模型ID
    ModelReplaceRequest:
      required:
        - id
        - productId
      type: object
      description: 替换模型
      allOf:
        - $ref: '#/components/schemas/SceneDocumentOperateRequest'
        - type: object
          properties:
            id:
              type: string
              description: 需要替换的模型ID
            productId:
              type: string
              description: 商品ID
            transform:
              $ref: '#/components/schemas/Matrix'
    ModelUpdateRequest:
      required:
        - id
        - transform
      type: object
      description: 更新模型
      allOf:
        - $ref: '#/components/schemas/SceneDocumentOperateRequest'
        - type: object
          properties:
            id:
              type: string
              description: 需要更新的模型ID
            transform:
              $ref: '#/components/schemas/Matrix'
    SceneDocumentBatchUpdateRequest:
      type: object
      properties:
        requestId:
          type: string
          description: 请求ID，用于幂等性控制
        operationId:
          type: string
          description: 操作ID，用于异步操作跟踪
        batchRequests:
          type: array
          items:
            $ref: '#/components/schemas/SceneDocumentOperateRequest'
    SceneDocumentOperateRequest:
      required:
        - opType
      type: object
      properties:
        opType:
          type: string
          description: 场景操作方式
      discriminator:
        propertyName: opType
        mapping:
          model_create: '#/components/schemas/ModelCreateRequest'
          model_update: '#/components/schemas/ModelUpdateRequest'
          model_replace: '#/components/schemas/ModelReplaceRequest'
          model_delete: '#/components/schemas/ModelDeleteRequest'
          light_create: '#/components/schemas/LightCreateRequest'
          light_update: '#/components/schemas/LightUpdateRequest'
          light_delete: '#/components/schemas/LightDeleteRequest'
    SpectrumData:
      type: object
      properties:
        spectrumId:
          type: string
          description: 光谱id（LED光谱必传）
        type:
          type: string
          description: 光谱类型，UNSPECIFIED(未定义)，PLANCK(普朗克光谱)，LED(LED光谱)，RGB(RGB光谱)
          enum:
            - UNSPECIFIED
            - PLANCK
            - LED
            - RGB
        colorTemperature:
          type: integer
          description: 色温，1000K~25000K（默认普朗克光谱4500K）
          format: int32
        color:
          $ref: '../../common/geom-data-exchange-schemas.yaml#/components/schemas/Point3d'
      description: 场景光谱数据
    Light:
      type: object
      description: 照明灯光
      allOf:
        - $ref: '#/components/schemas/SceneElement'
        - type: object
          properties:
            iesSpotLightData:
              $ref: '#/components/schemas/IesSpotLightData'
            bindingModelIds:
              type: array
              description: ies绑定的模型ID列表
              items:
                type: string
                description: ies绑定的模型ID列表
    Model:
      type: object
      description: 模型
      allOf:
        - $ref: '#/components/schemas/SceneElement'
        - type: object
          properties:
            productId:
              type: string
              description: 商品ID
            transform:
              $ref: '#/components/schemas/Matrix'
    SceneDocument:
      type: object
      properties:
        sceneElements:
          type: array
          description: 每个element的更新结果
          items:
            $ref: '#/components/schemas/SceneElement'
        version:
          type: string
          description: 版本号
      description: 更新后的场景文档
    SceneDocumentBatchUpdateResponse:
      type: object
      properties:
        elements:
          type: array
          items:
            $ref: '#/components/schemas/SceneElementUpdateResponse'
        sceneDocument:
          $ref: '#/components/schemas/SceneDocument'
    SceneElement:
      required:
        - elemetType
      type: object
      properties:
        id:
          type: string
          description: 元素ID
        version:
          type: string
          description: 版本号
        elemetType:
          type: string
          description: 元素类型
      discriminator:
        propertyName: elemetType
        mapping:
          model: '#/components/schemas/Model'
          light: '#/components/schemas/Light'
    SceneElementUpdateResponse:
      type: object
      properties:
        elementIds:
          type: array
          description: 处理成功的元素id
          items:
            type: string
            description: 处理成功的元素id
