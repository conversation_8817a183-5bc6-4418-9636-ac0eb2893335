#!/bin/bash

# 动态内容完整生成脚本
# 一次性生成 OpenAPI 配置、Sidebar、导航菜单和文档内容

set -e

# 检查是否在CI环境中运行
CI_MODE=${CI:-false}

if [[ "$CI_MODE" == "true" ]]; then
    echo "[CI-INFO] 🚀 CI环境 - 开始生成所有动态内容..."
else
    echo "🚀 开始生成所有动态内容..."
fi
echo ""

# 获取脚本目录
SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"
PROJECT_ROOT="$(dirname "$(dirname "$(dirname "$SCRIPT_DIR")")")"
DOCS_SITE_DIR="$PROJECT_ROOT/documentation/website"

# 确保在正确的目录中
cd "$PROJECT_ROOT"

echo "📁 项目根目录: $PROJECT_ROOT"
echo "📁 文档站点目录: $DOCS_SITE_DIR"
echo ""

# 检查必要的文件是否存在
echo "🔍 检查必要文件..."

# 检查生成器脚本
if [ ! -f "generators/docs/scripts/generate-openapi-config.js" ]; then
    echo "❌ OpenAPI 配置生成器不存在: generators/docs/scripts/generate-openapi-config.js"
    exit 1
fi

if [ ! -f "generators/docs/scripts/generate-docs-content.js" ]; then
    echo "❌ 文档内容生成器不存在: generators/docs/scripts/generate-docs-content.js"
    exit 1
fi

# 检查 OpenAPI 文件
echo "🔍 检查 OpenAPI 文件..."

# 动态扫描 specifications/services 目录下的所有 openapi.yaml 文件
OPENAPI_FILES=()
if [ -d "specifications/services" ]; then
    echo "📁 扫描 specifications/services 目录..."

    # 遍历 specifications/services 目录下的所有子目录
    for service_dir in specifications/services/*/; do
        if [ -d "$service_dir" ]; then
            service_name=$(basename "$service_dir")

            # 跳过通用文件夹
            if [[ "$service_name" == "common" || "$service_name" == "shared" ]]; then
                echo "⏭️ 跳过通用目录: $service_name"
                continue
            fi

            openapi_file="${service_dir}openapi.yaml"
            if [ -f "$openapi_file" ]; then
                OPENAPI_FILES+=("$openapi_file")
                echo "✅ 找到: $openapi_file"
            else
                echo "⚠️ 未找到 openapi.yaml 在: $service_dir"
            fi
        fi
    done
else
    echo "❌ specifications/services 目录不存在"
    exit 1
fi

# 检查是否找到了任何 OpenAPI 文件
if [ ${#OPENAPI_FILES[@]} -eq 0 ]; then
    echo "❌ 未找到任何 OpenAPI 规范文件 (openapi.yaml)"
    echo "   请确保在 specifications/services/*/openapi.yaml 路径下存在规范文件"
    exit 1
else
    echo "🎉 总共找到 ${#OPENAPI_FILES[@]} 个 OpenAPI 规范文件"
fi

# 验证所有找到的文件确实存在
MISSING_FILES=()
for file in "${OPENAPI_FILES[@]}"; do
    if [ ! -f "$file" ]; then
        MISSING_FILES+=("$file")
    fi
done

if [ ${#MISSING_FILES[@]} -gt 0 ]; then
    echo "⚠️ 以下 OpenAPI 文件检查失败:"
    for file in "${MISSING_FILES[@]}"; do
        echo "  - $file"
    done
    echo "   文档生成可能会出错，但继续执行..."
else
    echo "✅ 所有 OpenAPI 文件检查通过"
fi
echo ""

# 步骤 1: 生成 OpenAPI 配置
echo "📋 步骤 1: 生成 OpenAPI 配置..."
if cd documentation/website 2>/dev/null; then
    echo "📁 切换到 documentation/website 目录"
    
    # 确保有 node_modules (如果需要的话)
    if [[ "$CI_MODE" != "true" && ! -d "node_modules" ]]; then
        echo "📦 安装 npm 依赖..."
        npm install
    elif [[ "$CI_MODE" == "true" ]]; then
        echo "ℹ️ CI环境 - 跳过依赖安装（应已预先安装）"
    fi
    
    # 复制并运行 OpenAPI 配置生成器
    cp ../../generators/docs/scripts/generate-openapi-config.js ./temp-generate-config.js
    echo "🔄 执行 OpenAPI 配置生成..."
    node ./temp-generate-config.js
    rm -f ./temp-generate-config.js
    echo "✅ OpenAPI 配置生成完成"
    echo ""
else
    echo "❌ 无法切换到 documentation/website 目录"
    exit 1
fi

# 步骤 2: 生成动态文档内容
echo "📝 步骤 2: 生成动态文档内容..."
# 复制并运行文档内容生成器
cp ../../generators/docs/scripts/generate-docs-content.js ./temp-generate-docs.js
echo "🔄 执行文档内容生成..."
node ./temp-generate-docs.js
rm -f ./temp-generate-docs.js
echo "✅ 动态文档内容生成完成"
echo ""

# 步骤 3: 验证生成的文件
echo "🔍 步骤 3: 验证生成的文件..."

GENERATED_FILES=(
    "openapi-config.json"
    "navbar-config.json"
    "sidebars.ts"
    "docs/intro.md"
)

echo "检查生成的文件:"
for file in "${GENERATED_FILES[@]}"; do
    if [ -f "$file" ]; then
        echo "✅ $file"
    else
        echo "❌ $file (缺失)"
    fi
done

# 检查动态生成的服务概览页面
echo ""
echo "检查服务概览页面:"
if [ -f "openapi-config.json" ]; then
    node -e "
        const config = require('./openapi-config.json');
        const fs = require('fs');
        const services = config.summary?.services || [];
        
        services.forEach(service => {
            const serviceFile = \`docs/api/\${service.id}.md\`;
            if (fs.existsSync(serviceFile)) {
                console.log('✅ ' + serviceFile);
            } else {
                console.log('❌ ' + serviceFile + ' (缺失)');
            }
        });
    "
else
    echo "⚠️ 无法检查服务概览页面 - openapi-config.json 不存在"
fi
echo ""

# 步骤 4: 显示统计信息
echo "📊 步骤 4: 统计信息..."
if [ -f "openapi-config.json" ]; then
    SERVICE_COUNT=$(node -e "
        const config = require('./openapi-config.json');
        console.log(Object.keys(config.openApiConfig || {}).length);
    ")
    echo "🔢 检测到 $SERVICE_COUNT 个 API 服务"
    
    echo "📋 服务列表:"
    node -e "
        const config = require('./openapi-config.json');
        const services = config.summary?.services || [];
        services.forEach(service => {
            console.log('  - ' + service.name + ' (' + service.id + ')');
        });
    "
else
    echo "⚠️ 无法读取服务统计信息"
fi
echo ""

# 返回项目根目录
cd "$PROJECT_ROOT"

if [[ "$CI_MODE" == "true" ]]; then
    echo "[CI-SUCCESS] 🎉 所有动态内容生成完成!"
    echo ""
    echo "[CI-INFO] 📋 已生成的关键文件:"
    echo "[CI-INFO]   - documentation/website/openapi-config.json"
    echo "[CI-INFO]   - documentation/website/sidebars.ts"
    echo "[CI-INFO]   - documentation/website/docs/intro.md"
    echo ""
else
    echo "🎉 所有动态内容生成完成!"
    echo ""
    echo "📋 下一步建议:"
    echo "1. 检查生成的文件内容是否正确"
    echo "2. 运行 'cd documentation/website && npm run build' 测试构建"
    echo "3. 运行 'cd documentation/website && npm run start' 预览效果"
    echo ""
    echo "💡 提示: 当添加新的 OpenAPI 服务时，只需重新运行此脚本即可自动更新所有相关配置!" 
fi 