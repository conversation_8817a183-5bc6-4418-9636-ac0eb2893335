#!/bin/bash

# API 变更检测脚�?# 检�?OpenAPI 规范文件的变�?# 重构�?tools/ci-cd/detect-api-changes.sh
# 作�? Backend API Team

# 加载通用函数�?SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"
source "$SCRIPT_DIR/../utils/common.sh"

# 检测文件变更的通用函数
detect_changes_with_method() {
    local method="$1"
    local command="$2"
    local description="$3"
    
    log_info "🔍 $method: $description"
    log_debug "执行命令: $command"
    
    local changed_files=""
    if changed_files=$(eval "$command" 2>/dev/null | grep -E "backend-apis/specifications/services/.*/openapi\.yaml$" || true); then
        if [ -n "$changed_files" ]; then
            log_success "$method 检测到潜在变更"
            
            # 验证文件是否真实存在
            local verified_files=""
            echo "$changed_files" | while read -r file; do
                if [ -n "$file" ]; then
                    if [ -f "$file" ]; then
                        log_debug "文件存在: $file"
                    else
                        log_warning "文件不存�? $file (已被删除或移�?"
                    fi
                fi
            done
            
            # 构建验证后的文件列表
            verified_files=""
            for file in $changed_files; do
                if [ -n "$file" ] && [ -f "$file" ]; then
                    if [ -z "$verified_files" ]; then
                        verified_files="$file"
                    else
                        verified_files="$verified_files $file"
                    fi
                fi
            done
            
            if [ -n "$verified_files" ]; then
                log_success "$method 检测到有效变更: $verified_files"
                # 设置全局变量而不是输出到 stdout
                export DETECTED_FILES="$verified_files"
                return 0
            else
                log_warning "$method 检测到变更但文件不存在"
                export DETECTED_FILES=""
                return 1
            fi
        else
            log_info "$method 未检测到变更"
            export DETECTED_FILES=""
            return 1
        fi
    else
        log_warning "$method 执行失败或无输出"
        export DETECTED_FILES=""
        return 1
    fi
}

# 方法1: 使用 git diff 检测变�?detect_with_git_diff() {
    local base_ref="${1:-origin/master}"

    log_info "方法1: 使用 git diff 检测变�?

    # 只对远程分支执行 fetch 操作
    if [[ "$base_ref" == origin/* ]]; then
        # 确保有远程分支信�?        if ! git fetch origin >/dev/null 2>&1; then
            log_warning "无法获取远程分支信息"
            return 1
        fi
    fi

    local command="git diff --name-only $base_ref...HEAD"
    detect_changes_with_method "Git Diff" "$command" "对比 $base_ref 分支的变�?
}

# 方法2: 使用 git log 检测变�?detect_with_git_log() {
    local base_ref="${1:-origin/master}"
    
    log_info "方法2: 使用 git log 检测变�?
    
    local command="git log --name-only --pretty=format: $base_ref..HEAD"
    detect_changes_with_method "Git Log" "$command" "检查提交历史中的变�?
}

# 方法3: 使用 GitLab CI 变量检测变�?detect_with_ci_variables() {
    log_info "方法3: 使用 GitLab CI 变量检测变�?
    
    if [ -n "$CI_MERGE_REQUEST_DIFF_BASE_SHA" ] && [ -n "$CI_COMMIT_SHA" ]; then
        local command="git diff --name-only $CI_MERGE_REQUEST_DIFF_BASE_SHA..$CI_COMMIT_SHA"
        detect_changes_with_method "CI Variables" "$command" "使用 GitLab CI 提供�?SHA"
    else
        log_warning "GitLab CI 变量不可�?
        return 1
    fi
}

# 方法4: 检查最近的提交
detect_with_recent_commits() {
    local commit_count="${1:-5}"

    log_info "方法4: 检查最�?$commit_count 个提�?

    local command="git log --name-only --pretty=format: -n $commit_count"
    detect_changes_with_method "Recent Commits" "$command" "检查最近的提交"
}

# 方法5: 使用本地 git status 检测变更（用于本地开发）
detect_with_git_status() {
    log_info "方法5: 使用 git status 检测变�?

    # 检查已修改的文�?    local command="git diff --name-only HEAD"
    if detect_changes_with_method "Git Status (Modified)" "$command" "检查已修改的文�?; then
        return 0
    fi

    # 检查已暂存的文�?    command="git diff --name-only --cached"
    if detect_changes_with_method "Git Status (Staged)" "$command" "检查已暂存的文�?; then
        return 0
    fi

    # 检查未跟踪的文�?    command="git ls-files --others --exclude-standard"
    if detect_changes_with_method "Git Status (Untracked)" "$command" "检查未跟踪的文�?; then
        return 0
    fi

    return 1
}

# 主检测函�?detect_api_changes() {
    log_step "1" "开�?API 变更检�?

    # 确保在项目根目录下运�?    local project_root
    if [ -d "specifications" ]; then
        project_root="$(pwd)"
    elif [ -d "../specifications" ]; then
        project_root="$(cd .. && pwd)"
    elif [ -d "../../specifications" ]; then
        project_root="$(cd ../.. && pwd)"
    elif [ -d "../../../specifications" ]; then
        project_root="$(cd ../../.. && pwd)"
    else
        log_warning "无法找到项目根目录（specifications 目录），使用当前目录"
        project_root="$(pwd)"
    fi

    log_debug "项目根目�? $project_root"
    cd "$project_root" || {
        log_error "无法切换到项目根目录: $project_root"
        return 1
    }

    local detected_files=""
    local detection_method=""

    # 尝试多种检测方�?    log_info "尝试多种检测方�?.."
    
    # 方法1: Git diff with master (本地开发优先，检测已提交的变�?
    # 尝试多个master分支引用
    if detect_with_git_diff "master"; then
        detection_method="Git Diff (master)"
        detected_files="$DETECTED_FILES"
    elif detect_with_git_diff "origin/master"; then
        detection_method="Git Diff (origin/master)"
        detected_files="$DETECTED_FILES"
    # 方法2: Git status (检测未提交的变�?
    elif detect_with_git_status; then
        detection_method="Git Status"
        detected_files="$DETECTED_FILES"
    # 方法3: GitLab CI 变量
    elif detect_with_ci_variables; then
        detection_method="CI Variables"
        detected_files="$DETECTED_FILES"
    # 方法4: Git diff with develop
    elif detect_with_git_diff "develop"; then
        detection_method="Git Diff (develop)"
        detected_files="$DETECTED_FILES"
    elif detect_with_git_diff "origin/develop"; then
        detection_method="Git Diff (origin/develop)"
        detected_files="$DETECTED_FILES"
    # 方法5: 最近提�?    elif detect_with_recent_commits 10; then
        detection_method="Recent Commits"
        detected_files="$DETECTED_FILES"
    # 方法6: Git log
    elif detect_with_git_log "master"; then
        detection_method="Git Log (master)"
        detected_files="$DETECTED_FILES"
    elif detect_with_git_log "origin/master"; then
        detection_method="Git Log (origin/master)"
        detected_files="$DETECTED_FILES"
    fi
    
    # 处理检测结�?    if [ -n "$detected_files" ]; then
        log_success "�?检测到 API 变更 (方法: $detection_method)"
        
        # 设置环境变量
        export HAS_API_CHANGES="true"
        export CHANGED_API_FILES="$detected_files"
        export DETECTION_METHOD="$detection_method"
        
        # 统计变更文件数量
        local file_count=$(echo "$detected_files" | wc -w)
        increment_counter "changed_files" "$file_count"
        
        log_info "变更文件数量: $file_count"
        log_info "变更文件列表:"
        for file in $detected_files; do
            echo "  - $file"
        done
        
        return 0
    else
        log_info "�?未检测到 API 变更"
        
        # 设置环境变量
        export HAS_API_CHANGES="false"
        export CHANGED_API_FILES=""
        export DETECTION_METHOD="none"
        
        return 0
    fi
}

# 分析变更的服�?analyze_changed_services() {
    log_step "2" "分析变更的服�?
    
    if [ "$HAS_API_CHANGES" != "true" ]; then
        log_info "�?API 变更，跳过服务分�?
        return 0
    fi
    
    local changed_services=""
    local services=$(get_services)
    
    for file in $CHANGED_API_FILES; do
        # 从文件路径提取服务名
        if [[ "$file" =~ backend-apis/specifications/services/([^/]+)/openapi\.yaml ]]; then
            local service="${BASH_REMATCH[1]}"
            
            # 验证服务是否有效
            if is_valid_service "$service"; then
                if [[ ! "$changed_services" =~ $service ]]; then
                    changed_services="$changed_services $service"
                    increment_counter "changed_services"
                    log_info "检测到服务变更: $service"
                fi
            else
                log_warning "无效的服务名: $service"
            fi
        fi
    done
    
    # 设置环境变量
    export CHANGED_SERVICES="$(echo "$changed_services" | xargs)"
    
    local service_count=$(get_counter "changed_services")
    if [ "$service_count" -gt 0 ]; then
        log_success "检测到 $service_count 个服务的变更: $CHANGED_SERVICES"
    else
        log_warning "未能识别出具体的变更服务"
    fi
}

# 生成变更摘要
generate_change_summary() {
    log_step "3" "生成变更摘要"
    
    local summary=""
    
    if [ "$HAS_API_CHANGES" = "true" ]; then
        local file_count=$(get_counter "changed_files")
        local service_count=$(get_counter "changed_services")
        
        summary="检测到 API 变更:\n"
        summary="${summary}- 检测方�? $DETECTION_METHOD\n"
        summary="${summary}- 变更文件: $file_count 个\n"
        summary="${summary}- 影响服务: $service_count �?
        
        if [ -n "$CHANGED_SERVICES" ]; then
            summary="${summary} ($CHANGED_SERVICES)"
        fi
        
        summary="${summary}\n\n变更文件列表:\n"
        for file in $CHANGED_API_FILES; do
            summary="${summary}- $file\n"
        done
    else
        summary="未检测到 API 变更"
    fi
    
    # 设置环境变量
    export API_CHANGES="$summary"
    
    log_info "变更摘要:"
    echo -e "$summary"
}

# 显示检测结�?show_detection_results() {
    log_step "4" "显示检测结�?
    
    echo ""
    log_info "🔍 API 变更检测结果汇�?"
    echo "  �?API 变更: $HAS_API_CHANGES"
    echo "  检测方�? $DETECTION_METHOD"
    echo "  变更文件: $CHANGED_API_FILES"
    echo "  变更服务: $CHANGED_SERVICES"
    
    show_stats "API 变更检测统�?
}

# 主函�?main() {
    init_script "API 变更检�? "检�?OpenAPI 规范文件的变�?
    
    # 执行检测流�?    detect_api_changes
    analyze_changed_services
    generate_change_summary
    show_detection_results
    
    log_success "🎉 API 变更检测完成！"
    finish_script "API 变更检�? "true"
}

# 如果直接执行此脚�?if [[ "${BASH_SOURCE[0]}" == "${0}" ]]; then
    main "$@"
fi

log_info "API 变更检测模块已加载 �?


