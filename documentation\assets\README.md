# 静态资源管理

这个目录包含了 API Registry 文档系统使用的所有静态资源文件。

## 📁 目录结构

### 🖼️ images/ - 图片资源
```
images/
├── frontend/                 # 前端相关图片
│   ├── miniapp-screenshots/ # 小程序截图
│   ├── sdk-diagrams/        # SDK 架构图
│   └── ui-components/       # UI 组件图片
├── backend/                 # 后端相关图片
│   ├── api-flow/           # API 流程图
│   ├── architecture/       # 后端架构图
│   └── database-schema/    # 数据库结构图
└── architecture/           # 整体架构图片
    ├── system-overview/    # 系统概览图
    ├── data-flow/         # 数据流图
    └── deployment/        # 部署架构图
```

### 📊 diagrams/ - 图表文件
```
diagrams/
├── sequence/               # 时序图
│   ├── authentication.puml # 认证流程时序图
│   ├── data-sync.puml     # 数据同步时序图
│   └── api-calls.puml     # API 调用时序图
├── architecture/          # 架构图
│   ├── system-arch.drawio # 系统架构图
│   ├── microservices.drawio # 微服务架构图
│   └── deployment.drawio  # 部署架构图
└── flow/                  # 流程图
    ├── user-journey.drawio # 用户旅程图
    ├── development.drawio  # 开发流程图
    └── ci-cd.drawio       # CI/CD 流程图
```

### 🎨 icons/ - 图标文件
```
icons/
├── api-types/             # API 类型图标
│   ├── rest-api.svg      # REST API 图标
│   ├── graphql.svg       # GraphQL 图标
│   └── websocket.svg     # WebSocket 图标
├── platforms/             # 平台图标
│   ├── web.svg           # Web 平台
│   ├── mobile.svg        # 移动平台
│   └── desktop.svg       # 桌面平台
└── status/                # 状态图标
    ├── success.svg        # 成功状态
    ├── warning.svg        # 警告状态
    └── error.svg          # 错误状态
```

### 🏢 logos/ - Logo 文件
```
logos/
├── kujiale/               # 酷家乐 Logo
│   ├── logo-full.svg     # 完整 Logo
│   ├── logo-icon.svg     # 图标 Logo
│   └── logo-text.svg     # 文字 Logo
├── partners/              # 合作伙伴 Logo
└── technologies/          # 技术栈 Logo
    ├── react.svg         # React Logo
    ├── vue.svg           # Vue Logo
    ├── java.svg          # Java Logo
    └── typescript.svg    # TypeScript Logo
```

### 📸 screenshots/ - 截图文件
```
screenshots/
├── frontend/              # 前端截图
│   ├── miniapp/          # 小程序截图
│   │   ├── home-page.png # 首页截图
│   │   ├── editor.png    # 编辑器截图
│   │   └── preview.png   # 预览截图
│   └── web/              # Web 端截图
└── backend/              # 后端截图
    ├── admin-panel/      # 管理面板截图
    ├── api-docs/         # API 文档截图
    └── monitoring/       # 监控面板截图
```

## 📝 使用规范

### 文件命名规范
- 使用小写字母和连字符：`user-profile.png`
- 包含版本信息：`api-flow-v2.svg`
- 描述性命名：`authentication-sequence-diagram.puml`

### 图片格式选择
- **PNG**: 截图、复杂图像
- **SVG**: 图标、简单图形、Logo
- **JPG**: 照片、复杂色彩图像
- **WebP**: 现代浏览器优化图像

### 图片尺寸建议
- **截图**: 1920x1080 或 1280x720
- **图标**: 24x24, 32x32, 48x48, 64x64
- **Logo**: 矢量格式（SVG）优先
- **架构图**: 适合文档显示的尺寸

## 🔗 在文档中引用资源

### Markdown 中的引用
```markdown
# 相对路径引用
![系统架构图](../assets/diagrams/architecture/system-arch.png)

# 带标题的引用
![API 调用流程](../assets/diagrams/sequence/api-calls.png "API 调用时序图")
```

### HTML 中的引用
```html
<!-- 响应式图片 -->
<img src="../assets/images/frontend/miniapp-screenshots/home-page.png" 
     alt="小程序首页" 
     style="max-width: 100%; height: auto;">

<!-- SVG 图标 -->
<img src="../assets/icons/api-types/rest-api.svg" 
     alt="REST API" 
     width="24" height="24">
```

## 🛠️ 图表工具推荐

### 时序图
- **PlantUML**: 代码生成时序图
- **Mermaid**: Markdown 兼容的图表
- **Draw.io**: 在线图表编辑器

### 架构图
- **Draw.io**: 免费在线工具
- **Lucidchart**: 专业图表工具
- **Visio**: Microsoft 图表工具

### 流程图
- **Figma**: 设计和原型工具
- **Sketch**: Mac 平台设计工具
- **Adobe XD**: Adobe 设计套件

## 📋 资源清单

### 必需资源
- [ ] 系统整体架构图
- [ ] 前端小程序截图
- [ ] 后端 API 文档截图
- [ ] 认证流程时序图
- [ ] 数据流程图

### 可选资源
- [ ] 用户旅程图
- [ ] 部署架构图
- [ ] 性能监控截图
- [ ] 错误处理流程图

## 🤝 贡献资源

### 提交新资源
1. 确保资源符合命名规范
2. 提供适当的文件格式
3. 包含必要的说明文档
4. 遵循版权和许可要求

### 更新现有资源
1. 保留原文件名
2. 更新版本号
3. 提供变更说明
4. 确保向后兼容性

## 📞 联系我们

如果您需要特定的图片或图表，或者有资源相关的问题：
- 邮箱：<EMAIL>
- 内部群：API Registry 文档组
