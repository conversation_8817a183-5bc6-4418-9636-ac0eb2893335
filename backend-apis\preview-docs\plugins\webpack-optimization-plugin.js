/**
 * 自定义Docusaurus插件：Webpack优化配置
 * 解决大字符串序列化警告和提升构建性能
 */

const fs = require('fs');
const path = require('path');

/**
 * 动态扫描API服务目录，自动发现所有服务
 */
function scanApiServices(docsPath) {
  const apiPath = path.join(docsPath, 'api');
  
  if (!fs.existsSync(apiPath)) {
    return [];
  }
  
  try {
    return fs.readdirSync(apiPath, { withFileTypes: true })
      .filter(dirent => dirent.isDirectory())
      .map(dirent => dirent.name)
      .sort(); // 确保顺序一致
  } catch (error) {
    console.warn(`⚠️ 无法扫描API服务目录: ${error.message}`);
    return [];
  }
}

/**
 * 为每个API服务生成缓存组配置
 */
function generateApiCacheGroups(services) {
  const cacheGroups = {};
  
  services.forEach(service => {
    const serviceName = service.toLowerCase().replace(/[^a-z0-9]/g, '-');
    cacheGroups[`api-${serviceName}`] = {
      name: `api-${serviceName}`,
      test: new RegExp(`[\\\\/]docs[\\\\/]api[\\\\/]${service}[\\\\/]`),
      priority: 20,
      reuseExistingChunk: true,
      chunks: 'all',
      // 为大型API文档设置较大的minSize
      minSize: 10000,
      maxSize: 2000000, // 2MB
    };
  });
  
  return cacheGroups;
}

module.exports = function webpackOptimizationPlugin(context, options) {
  return {
    name: 'webpack-optimization-plugin',
    configureWebpack(config, isServer, utils) {
      // 动态扫描API服务
      const docsPath = path.join(context.siteDir, 'docs');
      const apiServices = scanApiServices(docsPath);
      
      console.log(`🔍 发现 ${apiServices.length} 个API服务:`, apiServices.join(', '));
      return {
        // 缓存优化 - 减少大字符串序列化问题
        cache: config.cache ? {
          ...config.cache,
          type: 'filesystem',
          compression: 'gzip',
          maxMemoryGenerations: 0, // 禁用内存缓存，只使用文件系统缓存
          maxAge: 5184000000, // 60天
          store: 'pack',
          // 针对大型API文档的特殊缓存策略
          buildDependencies: {
            config: [__filename],
          },
          // 缓存分组策略
          cacheDirectory: config.cache.cacheDirectory,
          name: `${config.cache.name || 'default'}-optimized`,
          version: '1.1.0',
          // 针对大字符串使用Buffer处理
          serialize: function(obj, context) {
            // 对于大于1MB的字符串，使用Buffer进行序列化
            if (typeof obj === 'string' && obj.length > 1024 * 1024) {
              return Buffer.from(obj, 'utf8');
            }
            return obj;
          },
        } : undefined,

        // 性能配置
        performance: {
          hints: process.env.NODE_ENV === 'production' ? 'warning' : false,
          maxAssetSize: 5000000, // 5MB
          maxEntrypointSize: 5000000, // 5MB
          assetFilter: function(assetFilename) {
            // 忽略map文件和YAML文件的性能警告
            return !(/\.map$/.test(assetFilename) || /\.ya?ml$/.test(assetFilename));
          },
        },

        // 模块规则优化
        module: {
          rules: [
            // YAML文件处理优化
            {
              test: /\.ya?ml$/,
              type: 'asset/source',
              generator: {
                dataUrl: {
                  mimetype: 'text/yaml',
                },
              },
            },
            // MDX文件处理优化 - 针对大型API文档
            {
              test: /\.mdx?$/,
              include: /docs\/api/,
              use: [
                {
                  loader: '@docusaurus/mdx-loader',
                  options: {
                    // 启用MDX的压缩和优化
                    remarkPlugins: [],
                    rehypePlugins: [],
                    // 对于大型schema，使用更高效的序列化
                    format: 'mdx',
                    development: process.env.NODE_ENV !== 'production',
                  },
                },
              ],
            },
          ],
        },

        // 代码分割优化
        optimization: {
          splitChunks: {
            chunks: 'all',
            minSize: 20000,
            maxSize: 2000000, // 2MB最大chunk
            cacheGroups: {
              // 动态生成的API服务缓存组
              ...generateApiCacheGroups(apiServices),
              // 其他API文档
              openapi: {
                name: 'openapi-docs',
                test: /[\\/]docs[\\/]api[\\/]/,
                priority: 15,
                reuseExistingChunk: true,
                chunks: 'all',
              },
              // 第三方依赖单独打包
              vendor: {
                name: 'vendor',
                test: /[\\/]node_modules[\\/]/,
                priority: 5,
                reuseExistingChunk: true,
                chunks: 'all',
              },
            },
          },
          // 仅在客户端构建时启用
          ...(isServer ? {} : {
            usedExports: true,
            sideEffects: false,
          }),
        },

        // Resolve优化
        resolve: {
          cacheWithContext: false,
          symlinks: false,
        },
      };
    },
  };
};