openapi: 3.1.0
info:
  title: Geom Data Exchange Schemas
  description: |-
    Geom Data Exchange 数据结构 OpenAPI 规范文档.

    本文档定义了 Geom Data Exchange 中所有数据传输对象的结构规范.
  version: 1.0.0
  contact:
    name: Qunhe Tech
    email: <EMAIL>
  license:
    name: Proprietary
    url: https://www.manycore.com/license

components:
  schemas:
    Point2d:
      type: object
      title: Point2d
      description: 二维点。
      properties:
        x:
          type: number
          format: double
          description: x坐标。
        y:
          type: number
          format: double
          description: y坐标。

    Point3d:
      type: object
      title: Point3d
      description: 三维点。
      properties:
        x:
          type: number
          format: double
          description: x坐标。
        y:
          type: number
          format: double
          description: y坐标。
        z:
          type: number
          format: double
          description: z坐标。

    Vector2d:
      type: object
      title: Vector2d
      description: 二维向量。
      properties:
        x:
          type: number
          format: double
          description: 二维向量的x坐标。
        y:
          type: number
          format: double
          description: 二维向量的y坐标。

    Vector3d:
      type: object
      title: Vector3d
      description: 三维向量。
      properties:
        x:
          type: number
          format: double
          description: 三维向量的x坐标。
        y:
          type: number
          format: double
          description: 三维向量的y坐标。
        z:
          type: number
          format: double
          description: 三维向量的z坐标。

    Interval:
      type: object
      title: Interval
      description: 参数区间。
      properties:
        s:
          type: number
          format: double
          description: 参数区间的起点。
        e:
          type: number
          format: double
          description: 参数区间的终点。

    UV:
      type: object
      title: UV
      description: 二维参数域内的UV点。
      properties:
        u:
          type: number
          format: double
          description: 二维参数域的第一个参数u。
        v:
          type: number
          format: double
          description: 二维参数域的第二个参数v。

    Matrix:
      type: object
      title: Matrix
      description: 变换矩阵。不区分3x3，4x4，按数组长度来判断；矩阵元素按列向量方式排成一维数组。
      properties:
        elements:
          type: array
          items:
            type: number
            format: double
          description: 矩阵元素按列向量方式排成一维数组。

    CCS2d:
      type: object
      title: CCS2d
      description: 二维局部坐标系。
      properties:
        o:
          $ref: '#/components/schemas/Point2d'
          description: 坐标系原点。
        dx:
          $ref: '#/components/schemas/Vector2d'
          description: 坐标系x轴方向。
        

    CCS3d:
      type: object
      title: CCS3d
      description: 三维局部坐标系。
      properties:
        o:
          $ref: '#/components/schemas/Point3d'
          description: 坐标系原点。
        dx:
          $ref: '#/components/schemas/Vector3d'
          description: 坐标系x轴方向。
        dy:
          $ref: '#/components/schemas/Vector3d'
          description: 坐标系y轴方向。
        

    Geometry:
      type: object
      title: Geometry
      description: 所有几何数据的基类。注意，所有几何数据都是SubType。
      

    Curve2d:
      type: object
      title: Curve2d
      description: 二维曲线的基类。
      allOf:
        - $ref: '#/components/schemas/Geometry'
      discriminator:
        propertyName: tp
        mapping:
          L2: '#/components/schemas/Line2d'
          LS2: '#/components/schemas/LineSegment2d'
          RAY2: '#/components/schemas/Ray2d'
          ARC2: '#/components/schemas/Arc2d'
          CIR2: '#/components/schemas/Circle2d'
          ELP2: '#/components/schemas/Ellipse2d'
          EARC2: '#/components/schemas/EllipticalArc2d'
          PLY2: '#/components/schemas/Polyline2d'
          BEZ2: '#/components/schemas/BezierCurve2d'
          NBSC2: '#/components/schemas/NurbsCurve2d'
      required:
        - tp

    Curve3d:
      type: object
      title: Curve3d
      description: 三维曲线的基类。
      allOf:
        - $ref: '#/components/schemas/Geometry'
      discriminator:
        propertyName: tp
        mapping:
          L: '#/components/schemas/Line3d'
          LS: '#/components/schemas/LineSegment3d'
          RAY: '#/components/schemas/Ray3d'
          CIR: '#/components/schemas/Circle3d'
          ARC: '#/components/schemas/Arc3d'
          BEZ: '#/components/schemas/BezierCurve3d'
          ELP: '#/components/schemas/Ellipse3d'
          EARC3: '#/components/schemas/EllipticalArc3d'
          PLY: '#/components/schemas/Polyline3d'
          NBSC3: '#/components/schemas/NurbsCurve3d'
          COS: '#/components/schemas/CurveOnSurface'
          COSS: '#/components/schemas/CurveOnSurfaces'
      required:
        - tp

    Surface:
      type: object
      title: Surface
      description: 所有曲面的基类。
      allOf:
        - $ref: '#/components/schemas/Geometry'
      discriminator:
        propertyName: tp
        mapping:
          P: '#/components/schemas/Plane'
          CYL: '#/components/schemas/Cylinder'
          TOR: '#/components/schemas/Torus'
          SLE: '#/components/schemas/GenericCylinder'
          CON: '#/components/schemas/Cone'
          RTS: '#/components/schemas/RectTrimmedSurface'
          GTS: '#/components/schemas/TrimmedSurface'
          NBSS: '#/components/schemas/NurbsSurface'
          REVS: '#/components/schemas/RevolvedSurface'
      required:
        - tp

    Line2d:
      type: object
      title: Line2d
      description: 二维直线。
      allOf:
        - $ref: '#/components/schemas/Curve2d'
      properties:
        tp:
          type: string
          enum: [L2]
        p0:
          $ref: '#/components/schemas/Point2d'
          description: 直线的原点。
        v:
          $ref: '#/components/schemas/Vector2d'
          description: 直线的方向。

    Line3d:
      type: object
      title: Line3d
      description: 三维直线。
      allOf:
        - $ref: '#/components/schemas/Curve3d'
      properties:
        tp:
          type: string
          enum: [L]
        p0:
          $ref: '#/components/schemas/Point3d'
          description: 直线的原点。
        v:
          $ref: '#/components/schemas/Vector3d'
          description: 直线的方向（单位向量）。

    TrimmedCurve2d:
      type: object
      title: TrimmedCurve2d
      description: 二维裁剪曲线（有界曲线）。
      x-abstract: true  # 标记为抽象类，避免生成文档页面
      x-internal: true  # 标记为内部类型，简化文档展示
      x-circular: true  # 标记循环引用，让插件知道这是循环的一部分
      x-circular-ref:
        - Curve2d
        - description: "TrimmedCurve2d继承自Curve2d，其子类可能通过属性引用回Curve2d的其他子类。"
      allOf:
        - $ref: '#/components/schemas/Curve2d'
      properties:
        startParam:
          type: number
          format: double
          readOnly: true
          description: 获取和设置裁剪曲线的起点参数。
        endParam:
          type: number
          format: double
          readOnly: true
          description: 获取和设置裁剪曲线的终点参数。
        

    TrimmedCurve3d:
      type: object
      title: TrimmedCurve3d
      description: 三维裁剪曲线（有界曲线）。
      x-abstract: true  # 标记为抽象类，避免生成文档页面
      x-internal: true  # 标记为内部类型，简化文档展示
      x-circular: true  # 标记循环引用，让插件知道这是循环的一部分
      x-circular-ref:
        - Curve3d
        - description: "TrimmedCurve3d继承自Curve3d，其子类可能通过属性引用回Curve3d的其他子类。"
      allOf:
        - $ref: '#/components/schemas/Curve3d'
      properties:
        startParam:
          type: number
          format: double
          description: 获取和设置裁剪曲线的起点参数。
        endParam:
          type: number
          format: double
          description: 获取和设置裁剪曲线的终点参数。
        

    LineSegment2d:
      type: object
      title: LineSegment2d
      description: 二维直线段。
      allOf:
        - $ref: '#/components/schemas/TrimmedCurve2d'
      properties:
        tp:
          type: string
          enum: [LS2]
        p0:
          $ref: '#/components/schemas/Point2d'
          description: 直线段的起点。
        p1:
          $ref: '#/components/schemas/Point2d'
          description: 直线段的终点。
        line:
          $ref: '#/components/schemas/Line2d'
          readOnly: true

    LineSegment3d:
      type: object
      title: LineSegment3d
      description: 三维直线段。
      allOf:
        - $ref: '#/components/schemas/TrimmedCurve3d'
      properties:
        tp:
          type: string
          enum: [LS]
        p0:
          $ref: '#/components/schemas/Point3d'
          description: 直线段的起点。
        p1:
          $ref: '#/components/schemas/Point3d'
          description: 直线段的终点。
        line:
          $ref: '#/components/schemas/Line3d'
          readOnly: true

    Ray2d:
      type: object
      title: Ray2d
      description: 二维射线。
      allOf:
        - $ref: '#/components/schemas/Curve2d'
      properties:
        tp:
          type: string
          enum: [RAY2]
        p0:
          $ref: '#/components/schemas/Point2d'
          description: 二维射线的原点。
        v:
          $ref: '#/components/schemas/Vector2d'
          description: 二维射线的方向（单位向量）。

    Ray3d:
      type: object
      title: Ray3d
      description: 三维射线。
      allOf:
        - $ref: '#/components/schemas/Curve3d'
      properties:
        tp:
          type: string
          enum: [RAY]
        p0:
          $ref: '#/components/schemas/Point3d'
          description: 射线的起点。
        v:
          $ref: '#/components/schemas/Vector3d'
          description: 射线的方向。

    Circle2d:
      type: object
      title: Circle2d
      description: 二维圆。
      allOf:
        - $ref: '#/components/schemas/Curve2d'
      properties:
        tp:
          type: string
          enum: [CIR2]
        c:
          $ref: '#/components/schemas/Point2d'
          description: 圆的圆心。
        r:
          type: number
          format: double
          description: 圆的半径。

    Circle3d:
      type: object
      title: Circle3d
      description: 三维圆。
      allOf:
        - $ref: '#/components/schemas/Curve3d'
      properties:
        tp:
          type: string
          enum: [CIR]
        ccs:
          $ref: '#/components/schemas/CCS3d'
          description: 三维圆的定位标架。
        r:
          type: number
          format: double
          description: 圆的半径。

    Arc2d:
      type: object
      title: Arc2d
      description: 二维圆弧。
      allOf:
        - $ref: '#/components/schemas/TrimmedCurve2d'
      properties:
        tp:
          type: string
          enum: [ARC2]
        circle:
          $ref: '#/components/schemas/Circle2d'
          description: 二维圆弧所在的圆。
        s:
          type: number
          format: double
          description: 二维圆弧的参数起点（弧度值）。
        e:
          type: number
          format: double
          description: 二维圆弧的参数终点（弧度值）。
        cs:
          type: integer
          description: 圆弧的转向：1 = 逆时针，-1 = 顺时针。

    Arc3d:
      type: object
      title: Arc3d
      description: 三维圆弧。
      allOf:
        - $ref: '#/components/schemas/TrimmedCurve3d'
      properties:
        tp:
          type: string
          enum: [ARC]
        circle:
          $ref: '#/components/schemas/Circle3d'
          description: 三维圆弧所在的圆。
        s:
          type: number
          format: double
          description: 三维圆弧的参数起点（弧度值）。
        e:
          type: number
          format: double
          description: 三维圆弧的参数终点（弧度值）。

    Ellipse2d:
      type: object
      title: Ellipse2d
      description: 二维椭圆。
      allOf:
        - $ref: '#/components/schemas/Curve2d'
      properties:
        tp:
          type: string
          enum: [ELP2]
        ccs2d:
          $ref: '#/components/schemas/CCS2d'
          description: 二维椭圆的局部坐标系（圆心，参数域x轴，参数域y轴）。
        a:
          type: number
          format: double
          description: 椭圆长轴半径，也就是x轴方向的半径。
        b:
          type: number
          format: double
          description: 椭圆短轴半径，也就是y轴方向的半径。
        c:
          $ref: '#/components/schemas/Point2d'
          description: 椭圆圆心坐标，为了兼容老版的Math2的json格式。

    Ellipse3d:
      type: object
      title: Ellipse3d
      description: 三维椭圆。
      allOf:
        - $ref: '#/components/schemas/Curve3d'
      properties:
        tp:
          type: string
          enum: [ELP]
        ccs:
          $ref: '#/components/schemas/CCS3d'
          description: 三维椭圆的局部坐标系（圆心，参数域x轴，参数域y轴，法向）。
        a:
          type: number
          format: double
          description: 椭圆长轴半径，也就是x轴方向的半径。
        b:
          type: number
          format: double
          description: 椭圆短轴半径，也就是y轴方向的半径。

    EllipticalArc2d:
      type: object
      title: EllipticalArc2d
      description: 二维椭圆弧。
      allOf:
        - $ref: '#/components/schemas/TrimmedCurve2d'
      properties:
        tp:
          type: string
          enum: [EARC2]
        ccs2d:
          $ref: '#/components/schemas/CCS2d'
          description: 二维椭圆弧所在的椭圆的局部坐标系。
        a:
          type: number
          format: double
          description: 椭圆长轴半径。
        b:
          type: number
          format: double
          description: 椭圆短轴半径。
        s:
          type: number
          format: double
          description: 椭圆弧的参数起点（弧度值）。
        e:
          type: number
          format: double
          description: 椭圆弧的参数终点（弧度值）。
        cs:
          type: integer
          description: 1 = 逆时针，-1 = 顺时针。

    EllipticalArc3d:
      type: object
      title: EllipticalArc3d
      description: 三维椭圆弧。
      allOf:
        - $ref: '#/components/schemas/TrimmedCurve3d'
      properties:
        tp:
          type: string
          enum: [EARC3]
        ccs3d:
          $ref: '#/components/schemas/CCS3d'
          description: 三维椭圆弧所在的椭圆的局部坐标系。
        a:
          type: number
          format: double
          description: 椭圆长轴半径。
        b:
          type: number
          format: double
          description: 椭圆短轴半径。
        s:
          type: number
          format: double
          description: 椭圆弧的参数起点（弧度值）。
        e:
          type: number
          format: double
          description: 椭圆弧的参数终点（弧度值）。

    Polyline2d:
      type: object
      title: Polyline2d
      description: 二维折线段。
      allOf:
        - $ref: '#/components/schemas/Curve2d'
      properties:
        tp:
          type: string
          enum: [PLY2]
        pts:
          type: array
          items:
            $ref: '#/components/schemas/Point2d'
          description: 构成折线段的有序点列。
        tangents:
          type: array
          items:
            $ref: '#/components/schemas/Vector2d'
          description: 折线段每点处的切向量；允许为空。

    Polyline3d:
      type: object
      title: Polyline3d
      description: 三维折线段。
      allOf:
        - $ref: '#/components/schemas/Curve3d'
      properties:
        tp:
          type: string
          enum: [PLY]
        pts:
          type: array
          items:
            $ref: '#/components/schemas/Point3d'
          description: 构成折线段的有序点列。
        tangents:
          type: array
          items:
            $ref: '#/components/schemas/Vector3d'
          description: 折线段每点处的切向量，允许为空。

    BezierCurve2d:
      type: object
      title: BezierCurve2d
      description: 二维贝塞尔曲线。
      allOf:
        - $ref: '#/components/schemas/Curve2d'
      properties:
        tp:
          type: string
          enum: [BEZ2]
        cv:
          type: array
          items:
            $ref: '#/components/schemas/Point2d'
          description: 贝塞尔曲线的控制顶点。
        itv:
          $ref: '#/components/schemas/Interval'
          description: 贝塞尔曲线的参数区间。

    BezierCurve3d:
      type: object
      title: BezierCurve3d
      description: 三维贝塞尔曲线。
      allOf:
        - $ref: '#/components/schemas/Curve3d'
      properties:
        tp:
          type: string
          enum: [BEZ]
        cv:
          type: array
          items:
            $ref: '#/components/schemas/Point3d'
          description: 贝塞尔曲线的控制顶点。
        itv:
          $ref: '#/components/schemas/Interval'
          description: 贝塞尔曲线的参数区间。

    NurbsCurve2d:
      type: object
      title: NurbsCurve2d
      description: 二维的非均匀有理B样条曲线。
      allOf:
        - $ref: '#/components/schemas/Curve2d'
      properties:
        tp:
          type: string
          enum: [NBSC2]
        d:
          type: integer
          description: nurbs曲线的次数。
        ks:
          type: array
          items:
            type: number
            format: double
          description: nurbs曲线的节点向量。
        cvs:
          type: array
          items:
            $ref: '#/components/schemas/Point2d'
          description: nurbs曲线的控制顶点。
        ws:
          type: array
          items:
            type: number
            format: double
          description: nurbs曲线控制顶点的权重。

    NurbsCurve3d:
      type: object
      title: NurbsCurve3d
      description: 三维的非均匀有理B样条曲线。
      allOf:
        - $ref: '#/components/schemas/Curve3d'
      properties:
        tp:
          type: string
          enum: [NBSC3]
        d:
          type: integer
          description: nurbs曲线的次数。
        ks:
          type: array
          items:
            type: number
            format: double
          description: nurbs曲线的节点向量。
        cvs:
          type: array
          items:
            $ref: '#/components/schemas/Point3d'
          description: nurbs曲线的控制顶点。
        ws:
          type: array
          items:
            type: number
            format: double
          description: nurbs曲线控制顶点的权重。

    NurbsSurface:
      type: object
      title: NurbsSurface
      description: 非均匀有理B样条曲面（NURBS）。
      allOf:
        - $ref: '#/components/schemas/Surface'
      properties:
        tp:
          type: string
          enum: [NBSS]
        du:
          type: integer
          description: nurbs曲面的u向次数。
        dv:
          type: integer
          description: nurbs曲面的v向次数。
        ksu:
          type: array
          items:
            type: number
            format: double
          description: nurbs曲面的u向节点向量。
        ksv:
          type: array
          items:
            type: number
            format: double
          description: nurbs曲面的v向节点向量。
        cvs:
          type: array
          items:
            type: array
            items:
              $ref: '#/components/schemas/Point3d'
          description: nurbs曲面的控制点。
        ws:
          type: array
          items:
            type: array
            items:
              type: number
              format: double
          description: nurbs曲面的控制点权重。

    CurveOnSurface:
      type: object
      title: CurveOnSurface
      description: 通过曲面参数域上的二维曲线定义的三维曲线或曲线段。
      allOf:
        - $ref: '#/components/schemas/Curve3d'
      properties:
        tp:
          type: string
          enum: [COS]
        pc:
          $ref: '#/components/schemas/Curve2d'
          description: 曲面参数域上定义的二维曲线（可能是裁剪曲线）。
        sur:
          $ref: '#/components/schemas/Surface'
          description: 背景曲面。

    CurveOnSurfaces:
      type: object
      title: CurveOnSurfaces
      description: 一条（贴近一簇曲面片的）三维曲线。
      allOf:
        - $ref: '#/components/schemas/Curve3d'
      properties:
        tp:
          type: string
          enum: [COSS]
        curv3d:
          $ref: '#/components/schemas/Curve3d'
          description: 贴近一簇曲面片的三维曲线。
        pCurvs:
          type: array
          items:
            $ref: '#/components/schemas/CurveOnSurface'
          description: 三维曲线投影在每个曲面上的形成的参数域二维曲线。
      x-circular-ref:
        - CurveOnSurface
        - description: "CurveOnSurfaces继承自Curve3d，其pCurvs属性引用CurveOnSurface数组，CurveOnSurface也继承自Curve3d，形成循环引用。"

    CompositeCurve2d:
      type: object
      title: CompositeCurve2d
      description: 二维组合曲线。
      properties:
        curves:
          type: array
          items:
            $ref: '#/components/schemas/Curve2d'
          description: 前后首尾相接的有界曲线列表。

    CompositeCurve3d:
      type: object
      title: CompositeCurve3d
      description: 三维复合曲线，由多段有界曲线按顺序组成的复合曲线。
      properties:
        curves:
          type: array
          items:
            $ref: '#/components/schemas/Curve3d'
          description: 前后首尾相接的有界曲线列表。

    CurveLoop2d:
      type: object
      title: CurveLoop2d
      description: 二维曲线环。
      properties:
        curves:
          type: array
          items:
            $ref: '#/components/schemas/Curve2d'
          description: 前后首尾相接成环的有界曲线列表。

    GeomRegion2d:
      type: object
      title: GeomRegion2d
      description: 不带拓扑信息的纯几何二维区域。
      properties:
        contour:
          $ref: '#/components/schemas/CurveLoop2d'
          description: 二维区域的外轮廓。
        holes:
          type: array
          items:
            $ref: '#/components/schemas/CurveLoop2d'
          description: 二维区域内部的空洞。

    SurfaceWithFrame:
      type: object
      title: SurfaceWithFrame
      description: 带局部标架的曲面。
      allOf:
        - $ref: '#/components/schemas/Surface'
      properties:
        ccs:
          $ref: '#/components/schemas/CCS3d'
          description: 三维曲面的局部标架（表达定位和朝向）。

    Plane:
      type: object
      title: Plane
      description: 平面。
      allOf:
        - $ref: '#/components/schemas/SurfaceWithFrame'
      properties:
        tp:
          type: string
          enum: [P]
        normal:
          $ref: '#/components/schemas/Vector3d'
          description: '为了兼容老版的Math2的json格式。'
        w:
          type: number
          format: double
          description: '为了兼容老版的Math2的json格式。'

    Cylinder:
      type: object
      title: Cylinder
      description: 圆柱面。
      allOf:
        - $ref: '#/components/schemas/SurfaceWithFrame'
      properties:
        tp:
          type: string
          enum: [CYL]
        r:
          type: number
          format: double
          description: 圆柱面半径。

    Cone:
      type: object
      title: Cone
      description: 圆锥面。
      allOf:
        - $ref: '#/components/schemas/SurfaceWithFrame'
      properties:
        tp:
          type: string
          enum: [CON]
        r:
          type: number
          format: double
          description: 圆锥底面半径。
        a:
          type: number
          format: double
          description: 圆锥母线和中心轴的夹角。

    Torus:
      type: object
      title: Torus
      description: 圆环面。
      allOf:
        - $ref: '#/components/schemas/SurfaceWithFrame'
      properties:
        tp:
          type: string
          enum: [TOR]
        R:
          type: number
          format: double
          description: 圆环面的大圆半径。
        r:
          type: number
          format: double
          description: 圆环面的小圆半径。

    GenericCylinder:
      type: object
      title: GenericCylinder
      description: 广义柱面（拉伸面）。
      allOf:
        - $ref: '#/components/schemas/Surface'
      properties:
        tp:
          type: string
          enum: [SLE]
        bc:
          $ref: '#/components/schemas/Curve2d'
          description: 在局部坐标系的xy平面上定义的拉伸面轮廓线。
        ccs:
          $ref: '#/components/schemas/CCS3d'
          description: 广义柱面的定位标架（xy平面上定义轮廓线，z轴方向为拉伸方向）。
        h:
          type: number
          format: double
          description: 沿局部坐标系的z轴方向的拉伸高度。

    RevolvedSurface:
      type: object
      title: RevolvedSurface
      description: 旋转面。
      allOf:
        - $ref: '#/components/schemas/Surface'
      properties:
        tp:
          type: string
          enum: [REVS]
        bc:
          $ref: '#/components/schemas/Curve3d'
          description: 旋转面的轮廓曲线。
        ax:
          $ref: '#/components/schemas/Line3d'
          description: 旋转面的旋转轴。
        s:
          type: number
          format: double
          description: 旋转面的起始旋转角度（弧度值）。
        e:
          type: number
          format: double
          description: 旋转面的终止旋转角度（弧度值）。

    TrimmedSurfaceBase:
      type: object
      title: TrimmedSurfaceBase
      description: 裁剪曲面（有界曲面）的基类。
      allOf:
        - $ref: '#/components/schemas/Surface'
      properties:
        sur:
          $ref: '#/components/schemas/Surface'
          description: 未裁剪的完整曲面。
        positive:
          type: boolean
          description: 裁剪曲面和曲面朝向是否一致。

    RectTrimmedSurface:
      type: object
      title: RectTrimmedSurface
      description: 对应到参数域上为矩形的裁剪曲面。
      allOf:
        - $ref: '#/components/schemas/TrimmedSurfaceBase'
      properties:
        tp:
          type: string
          enum: [RTS]
        min:
          $ref: '#/components/schemas/UV'
          description: 矩形参数域的左下角。
        max:
          $ref: '#/components/schemas/UV'
          description: 矩形参数域的右上角。

    TrimmedSurface:
      type: object
      title: TrimmedSurface
      description: 一般裁剪曲面（有界曲面）。
      allOf:
        - $ref: '#/components/schemas/TrimmedSurfaceBase'
      properties:
        tp:
          type: string
          enum: [GTS]
        contour:
          type: array
          items:
            $ref: '#/components/schemas/Curve3d'
          description: 裁剪曲面的外边界。
        holes:
          type: array
          items:
            type: array
            items:
              $ref: '#/components/schemas/Curve3d'
          description: 裁剪曲面的内边界（洞）。
        uvContour:
          type: array
          items:
            $ref: '#/components/schemas/Curve2d'
          description: 裁剪曲面的外边界在曲面参数域上对应的参数域曲线。
        uvHoles:
          type: array
          items:
            type: array
            items:
              $ref: '#/components/schemas/Curve2d'
          description: 裁剪曲面的内边界在曲面参数域上对应的参数域曲线。
      x-circular-ref:
        - Surface
        - Curve3d
        - description: "TrimmedSurface引用Surface和Curve3d，而CurveOnSurface等Curve3d子类又可能引用Surface，形成潜在循环引用。"

    # =====================================
    # Topo
    # =====================================
    Topology:
      type: object
      title: Topology
      description: 所有拓扑对象的基类。
      
      properties:
        n:
          $ref: '#/components/schemas/Name'
          description: 拓扑对象的持久命名。
        p:
          type: object
          additionalProperties: true
          description: 挂载在该对象上的非标数据（自定义属性），业务方自定义设置、解析和使用。
        id:
          type: string
          description: 该对象在内部系统中的ID。

    Name:
      type: object
      title: Name
      description: 名字的基类。
      discriminator:
        propertyName: subType
        mapping:
          TN: '#/components/schemas/TextName'
          STN: '#/components/schemas/StructuredName'
      required:
        - subType

    NameTypes:
      type: string
      title: NameTypes
      description: 名字的种类（仅用于业务逻辑，不参与序列化）。
      enum:
        - TEXT
        - STRUCT

    TextName:
      type: object
      title: TextName
      description: 文本式名字。
      allOf:
        - $ref: '#/components/schemas/Name'
      properties:
        subType:
          type: string
          enum: [TN]
        n:
          type: string
          description: 对象的名字。

    StructuredName:
      type: object
      title: StructuredName
      description: 结构化名字。
      allOf:
        - $ref: '#/components/schemas/Name'
      properties:
        subType:
          type: string
          enum: [STN]
        et:
          type: string
          description: 被命名对象的类型，例如V，E，F分别代表顶点，边，面。
        pg:
          type: array
          items:
            type: string
          description: 当前对象的生成源名字列表（字符串形式，避免循环引用）。
          maxItems: 50
        ac:
          type: array
          items:
            type: string
          description: 辅助关联对象名字列表（字符串形式，避免循环引用）。
          maxItems: 50
        pf:
          type: string
          description: 对象的额外编号或者额外类型信息。

    BodyBase:
      type: object
      title: BodyBase
      description: 所有几何体（模型）的基类。
      allOf:
        - $ref: '#/components/schemas/Topology'
      properties:
        modelType:
          $ref: '#/components/schemas/ModelTypes'
          readOnly: true

    ModelTypes:
      type: string
      title: ModelTypes
      description: 模型的分类。
      enum:
        - GEOMETRIC_2D_DESIGN
        - GEOMETRIC_3D_BODY
        - TRI_MESH_MODEL

    Body:
      type: object
      title: Body
      description: 三维几何造型体（solid body, sheet body, wire body）。
      allOf:
        - $ref: '#/components/schemas/BodyBase'
      properties:
        fs:
          type: array
          items:
            $ref: '#/components/schemas/Face'
          description: 造型体的所有面片。
        es:
          type: array
          items:
            $ref: '#/components/schemas/Edge'
          description: 造型体的所有边。
        vs:
          type: array
          items:
            $ref: '#/components/schemas/Vertex'
          description: 造型体的所有顶点。

    MeshBody:
      type: object
      title: MeshBody
      description: 三角网格。
      allOf:
        - $ref: '#/components/schemas/BodyBase'
      properties:
        vs:
          type: array
          items:
            type: number
            format: double
          description: 三角网格所有顶点的坐标组成的数组。
        fs:
          type: array
          items:
            type: integer
          description: 每个三角面片的三个顶点的下标构成的数组。
        otl:
          type: array
          items:
            $ref: '#/components/schemas/Outline'
          description: 额外标记的线条或者路径。

    Geometric2dDesign:
      type: object
      title: Geometric2dDesign
      description: 二维平面设计模型。
      allOf:
        - $ref: '#/components/schemas/BodyBase'
      properties:
        fs:
          type: array
          items:
            $ref: '#/components/schemas/Design2dFace'
          description: 2D设计的区域。
        es:
          type: array
          items:
            $ref: '#/components/schemas/Design2dEdge'
          description: 模型的所有边。
        vs:
          type: array
          items:
            $ref: '#/components/schemas/Design2dVertex'
          description: 模型的所有顶点。

    Component:
      type: object
      title: Component
      description: 组件。
      allOf:
        - $ref: '#/components/schemas/Topology'
      properties:
        mds:
          type: array
          items:
            $ref: '#/components/schemas/BodyBase'
          description: 组件包含的体。
        sgi:
          type: array
          items:
            $ref: '#/components/schemas/Instance'
          description: 组件包含的实例。
      x-circular-ref:
        - Instance
        - description: "Component可以包含Instance，Instance又引用Component，形成循环引用。序列化时应使用引用ID机制避免无限递归。"

    Instance:
      type: object
      title: Instance
      description: 组件实例（用于解决模型的复用）。
      allOf:
        - $ref: '#/components/schemas/Topology'
      properties:
        gp:
          oneOf:
            - $ref: '#/components/schemas/Component'
            - type: string
              description: 组件的引用ID，用于避免循环引用
          description: 当前实例所引用的组件。可以是完整的组件对象或组件ID引用。
        tm:
          $ref: '#/components/schemas/Matrix'
          description: 当前实例的变换矩阵。
      x-circular-ref:
        - Component
        - description: "Instance引用Component，Component又可以包含Instance，形成循环引用。建议在序列化时使用组件ID引用。"

    Vertex:
      type: object
      title: Vertex
      description: 三维几何模型的顶点。
      allOf:
        - $ref: '#/components/schemas/Topology'
      properties:
        pt:
          $ref: '#/components/schemas/Point3d'
          description: 顶点的坐标位置。

    Edge:
      type: object
      title: Edge
      description: 三维几何模型的边。
      allOf:
        - $ref: '#/components/schemas/Topology'
      properties:
        cv:
          $ref: '#/components/schemas/Curve3d'
          description: 边所依赖的曲线。
        sn:
          type: boolean
          description: 边的方向（从起点到终点）是否和所依赖的曲线的方向一致。
        vs:
          type: array
          items:
            $ref: '#/components/schemas/Vertex'
          description: 边的顶点。
      x-circular-ref:
        - Face
        - Vertex
        - description: "Edge引用Vertex，在拓扑结构中Edge和Face相互引用形成边界关系。"

    Face:
      type: object
      title: Face
      description: 三维几何模型的面片。
      allOf:
        - $ref: '#/components/schemas/Topology'
      properties:
        sf:
          $ref: '#/components/schemas/Surface'
          description: 三维面片所依赖的曲面。
        sn:
          type: boolean
          description: 面片的朝向是否和它所依赖的曲面的朝向一致。
        elp:
          type: array
          items:
            type: array
            items:
              $ref: '#/components/schemas/Edge'
          description: 面片的边界。
        esn:
          type: array
          items:
            type: array
            items:
              type: boolean
          description: 边界环本身的转向和其中每条边的方向是否一致。
        uvlp:
          type: array
          items:
            type: array
            items:
              $ref: '#/components/schemas/Curve2d'
          description: 边界环的每条边，在曲面的2维参数域空间当中对应的参数曲线。
        lps:
          type: array
          items:
            $ref: '#/components/schemas/Loop'
          description: 面片的Brep外环和内环。
      x-circular-ref:
        - Edge
        - Loop
        - description: "Face通过elp引用Edge，通过lps引用Loop，而Loop通过Fin又引用Edge，Edge可能在其他地方引用Face，形成复杂的拓扑循环引用。"

    Loop:
      type: object
      title: Loop
      description: 三维几何模型的BRep环。
      allOf:
        - $ref: '#/components/schemas/Topology'
      properties:
        fs:
          type: array
          items:
            $ref: '#/components/schemas/Fin'
          description: 构成环的翼边。

    Fin:
      type: object
      title: Fin
      description: 三维几何模型的翼边。
      allOf:
        - $ref: '#/components/schemas/Topology'
      properties:
        e:
          $ref: '#/components/schemas/Edge'
          description: 翼边所引用的边。
        sn:
          type: boolean
          description: 翼边的方向是否和所引用的边的方向一致。

    Design2dVertex:
      type: object
      title: Design2dVertex
      description: 二维平面设计模型的顶点。
      allOf:
        - $ref: '#/components/schemas/Topology'
      properties:
        pt:
          $ref: '#/components/schemas/Point2d'
          description: 顶点的坐标位置。

    Design2dEdge:
      type: object
      title: Design2dEdge
      description: 二维平面设计模型的边。
      allOf:
        - $ref: '#/components/schemas/Topology'
      properties:
        cv:
          $ref: '#/components/schemas/Curve2d'
          description: 边所依赖的曲线。
        sn:
          type: boolean
          description: 边的方向（从起点到终点）是否和所依赖的曲线的方向一致。
        vs:
          type: array
          items:
            $ref: '#/components/schemas/Design2dVertex'
          description: 边的两个顶点。

    Design2dFace:
      type: object
      title: Design2dFace
      description: 二维平面设计模型的一个区域（面片）。
      allOf:
        - $ref: '#/components/schemas/Topology'
      properties:
        elp:
          type: array
          items:
            type: array
            items:
              $ref: '#/components/schemas/Design2dEdge'
          description: 面片的边界。
        esn:
          type: array
          items:
            type: array
            items:
              type: boolean
          description: 边界环本身的转向和其中每条边的方向是否一致。

    Outline:
      type: object
      title: Outline
      description: 网格模型上额外标记出来的线条或线路。
      allOf:
        - $ref: '#/components/schemas/Topology'
      properties:
        vs:
          type: array
          items:
            type: integer
          description: 当前线条的两个顶点的下标。

    # =====================================
    # Other
    # =====================================
    MIX:
      type: object
      description: 序列化导出时的总包装对象。
      properties:
        tp:
          type: string
          description: MIX数据包自己本身的类型标记。
          example: 'MIX'
        v:
          type: string
          description: 当前MIX数据包的版本号。
          example: '0.0.1'
        d:
          type: array
          items:
            type: object
          description: 从可序列化对象转化而来的，可以直接json文本化的对象列表。 