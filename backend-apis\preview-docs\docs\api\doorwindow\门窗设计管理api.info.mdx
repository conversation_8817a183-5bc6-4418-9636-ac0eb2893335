---
id: 门窗设计管理api
title: "门窗设计管理API"
description: "门窗设计服务REST API"
sidebar_label: Introduction
sidebar_position: 0
hide_title: true
custom_edit_url: null
---

import ApiLogo from "@theme/ApiLogo";
import Heading from "@theme/Heading";
import SchemaTabs from "@theme/SchemaTabs";
import TabItem from "@theme/TabItem";
import Export from "@theme/ApiExplorer/Export";

<span
  className={"theme-doc-version-badge badge badge--secondary"}
  children={"Version: v1.0.0"}
>
</span>

<Export
  url={"/specifications/services/doorwindow/openapi.yaml"}
  proxy={undefined}
>
  
</Export>

<Heading
  as={"h1"}
  className={"openapi__heading"}
  children={"门窗设计管理API"}
>
</Heading>



门窗设计服务REST API

为群核旗下设计工具提供门窗模型的查询和批量更新操作。支持简单门窗（单一门窗单元）和复杂门窗（组合型门窗，如L型、U型等复杂结构）的完整生命周期管理。提供高效的批量操作和异步处理机制，确保大规模门窗数据处理的性能和稳定性。

**核心功能：**
- 门窗文档查询：获取指定楼层的完整门窗信息
- 批量更新操作：支持多种门窗操作类型的批量处理
- 多种门窗类型：简单门、简单窗、复杂门窗组合
- 多视图支持：BASIC、ADVANCED、FULL等不同详细程度的数据视图
- 异步处理：批量操作采用异步机制，保证系统响应性能
- 操作类型丰富：replace（替换）、attach（附加）、detach（分离）、update（更新）

**门窗类型支持：**
- 简单门窗：单一门窗单元，如普通门、普通窗
- 复杂门窗：组合型门窗，如L型窗、U型窗、转角门等
- 门类型：单开门、双开门、推拉门、折叠门等
- 窗类型：平开窗、推拉窗、百叶窗、落地窗等

**业务应用场景：**
- 建筑设计软件的门窗配置
- 门窗产品的三维建模和展示
- 建筑开口与门窗的关联管理
- 门窗规格和参数的批量调整
- 复杂门窗组合的快速创建
- 门窗数据的批量导入和同步

**技术规范：**
- 响应格式：JSON
- 字符编码：UTF-8
- 认证方式：基于设计方案的权限验证
- 批量限制：单次批量操作最多20个门窗
- 异步支持：长时间批量操作的异步处理机制

<div
  style={{"display":"flex","flexDirection":"column","marginBottom":"var(--ifm-paragraph-margin-bottom)"}}
>
  <h3
    style={{"marginBottom":"0.25rem"}}
  >
    Contact
  </h3><span>
    群核科技开发团队: [<EMAIL>](mailto:<EMAIL>)
  </span><span>
    URL: [https://wiki.manycore.com/doorwindow-design](https://wiki.manycore.com/doorwindow-design)
  </span>
</div><div
  style={{"marginBottom":"var(--ifm-paragraph-margin-bottom)"}}
>
  <h3
    style={{"marginBottom":"0.25rem"}}
  >
    License
  </h3><a
    href={"https://manycore.com/license"}
  >
    群核科技专有许可证
  </a>
</div>
      