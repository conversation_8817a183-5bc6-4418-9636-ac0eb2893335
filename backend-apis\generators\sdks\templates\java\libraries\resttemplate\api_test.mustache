{{>licenseInfo}}

package {{package}};
import {{invokerPackage}}.ApiClient;
{{#imports}}import {{import}};
{{/imports}}
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Disabled;
import org.junit.jupiter.api.Test;
import com.manycore.restapi.exception.RestApiClientException;
import com.manycore.restapi.operation.Operation;

import java.time.LocalDate;
import java.time.OffsetDateTime;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

{{#useBeanValidation}}
import {{javaxPackage}}.validation.constraints.*;
import {{javaxPackage}}.validation.Valid;

{{/useBeanValidation}}
/**
 * API tests for {{classname}}
 */
@Disabled
class {{classname}}Test {

    private {{classname}} api;

    @BeforeEach
    void setUp() {
        // 创建API客户端
        ApiClient apiClient = new ApiClient();

        // 设置基础路径（根据环境修改）
        apiClient.setBasePath("https://api-sit.kujiale.com");

        // 使用新增的快捷方法一步设置认证凭据
        apiClient.setAuthCredentials(
        "SlxOlcNIFu",     // 替换为您的AppKey
        "m2a4EikkxWfPMOMmsk7gaOXcKgz3Ensd",  // 替换为您的AppSecret
        "222"      // 替换为您的AppUid
        );

        // 创建API实例
        api = new {{classname}}(apiClient);
    }

    {{#operations}}{{#operation}}
    /**
     * {{summary}}
     *
     * {{notes}}
     *
     * @throws RestApiClientException
     *          if the Api call fails
     */
    @Test
    void {{operationId}}Test() {
        {{#allParams}}
        {{#isFile}}{{#useAbstractionForFiles}}{{#collectionFormat}}java.util.Collection<org.springframework.core.io.Resource>{{/collectionFormat}}{{^collectionFormat}}org.springframework.core.io.Resource{{/collectionFormat}}{{/useAbstractionForFiles}}{{^useAbstractionForFiles}}{{{dataType}}}{{/useAbstractionForFiles}}{{/isFile}}{{^isFile}}{{{dataType}}}{{/isFile}} {{paramName}} = null;
        {{/allParams}}

       

        // TODO: test validations
    }
    {{/operation}}{{/operations}}
}
